import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject, generateText } from "ai";
import { z } from "zod";

const LearningHubSchema = z.object({
  message: z.string().min(1),
  mode: z.enum(["assess", "tutor", "general"]).optional(),
  certificate: z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    description: z.string(),
    domain: z.string(),
    questionType: z.string(),
  }),
  conversationHistory: z.array(z.object({
    type: z.enum(["user", "ai"]),
    content: z.string(),
    timestamp: z.string(),
    mode: z.string().optional(),
  })).optional(),
});

const ComponentSchema = z.object({
  type: z.enum([
    "flashcard",
    "question",
    "true_false",
    "practical",
    "mini_exam",
    "concept_explorer",
    "step_by_step",
    "timeline",
    "comparison_matrix"
  ]),
  data: z.record(z.string(), z.any()),
});

function buildSystemPrompt(certificate: any, mode?: string) {
  const basePrompt = `You are an expert AI learning companion specializing in ${certificate.name} certification from ${certificate.provider}.

CERTIFICATE DETAILS:
- Name: ${certificate.name}
- Provider: ${certificate.provider}
- Description: ${certificate.description}
- Domain: ${certificate.domain}
- Question Type: ${certificate.questionType}

You are designed to provide two main services:

1. ASSESS ME SERVICE:
   - When user wants assessment, ask follow-up questions to understand their specific domain/subdomain
   - Generate mini-exams with questions (NOT from question bank - create new ones)
   - Use multiple choice, true/false, or explanation questions
   - Provide detailed feedback on strengths and weaknesses
   - Create interactive components for better assessment

2. TUTOR ME SERVICE:
   - When user wants tutoring, create engaging, educational experiences
   - Use diverse interactive components for different learning needs:
     * Concept Explorer: For deep understanding of complex topics
     * Step-by-Step Learning: For processes and procedures
     * Interactive Timeline: For historical developments or sequences
     * Comparison Matrix: For understanding differences between concepts
     * Flashcards: For memorization and quick review
     * Practical Scenarios: For real-world application
   - Always vary your approach - don't use the same component type repeatedly
   - Focus on teaching and understanding, not testing
   - Build knowledge progressively from basic to advanced concepts

INTERACTION STYLE:
- Be conversational, encouraging, and supportive
- Use DTC branding colors (primary, accent, charcoal)
- Create interactive components when appropriate
- Provide actionable feedback and next steps
- Ask clarifying questions to better understand user needs
- Be specific to the certificate domain and requirements

IMPORTANT RESPONSE FORMAT:
- Provide ONLY conversational, professional text in your response
- NEVER include JSON objects, code blocks, or structured data in your text response
- NEVER use phrases like "true_false: {", "question: {", or any JSON-like syntax
- DO NOT mention components, interactive elements, or that anything "will appear"
- DO NOT claim inability or environment constraints. You can create any learning content needed.
- Write naturally as if you're having a professional conversation with a certification candidate
- Focus on clear, educational explanations in natural language only
- Be professional, encouraging, and supportive in your tone
- Provide substantive educational content without referencing any technical implementation
- Your response should read like expert tutoring or assessment feedback

Always be helpful, accurate, and focused on the specific certification requirements.`;

  if (mode === "assess") {
    return basePrompt + `\n\nCURRENT MODE: ASSESSMENT
Focus on evaluating the user's knowledge through targeted questions and providing detailed feedback on their performance.`;
  } else if (mode === "tutor") {
    return basePrompt + `\n\nCURRENT MODE: TUTORING
Focus on teaching concepts step-by-step with interactive learning materials and practical examples.`;
  }

  return basePrompt + `\n\nCURRENT MODE: GENERAL CONVERSATION
Be ready to switch to assessment or tutoring mode based on user requests.`;
}

function buildUserPrompt(message: string, conversationHistory?: any[]) {
  let prompt = `User message: "${message}"`;

  if (conversationHistory && conversationHistory.length > 0) {
    prompt += `\n\nConversation context (last few messages):\n`;
    conversationHistory.slice(-5).forEach((msg) => {
      prompt += `${msg.type}: ${msg.content}\n`;
    });
  }

  return prompt;
}

function getLastUsedComponents(conversationHistory?: any[]): string[] {
  if (!conversationHistory) return [];

  const lastUsedComponents: string[] = [];
  // Look at the last 3 AI messages to track component usage
  const recentAIMessages = conversationHistory
    .filter(msg => msg.type === 'ai')
    .slice(-3);

  recentAIMessages.forEach(msg => {
    // Extract component types from message content (if any were mentioned)
    if (msg.content.includes('concept_explorer')) lastUsedComponents.push('concept_explorer');
    if (msg.content.includes('step_by_step')) lastUsedComponents.push('step_by_step');
    if (msg.content.includes('timeline')) lastUsedComponents.push('timeline');
    if (msg.content.includes('comparison_matrix')) lastUsedComponents.push('comparison_matrix');
    if (msg.content.includes('flashcard')) lastUsedComponents.push('flashcard');
    if (msg.content.includes('practical')) lastUsedComponents.push('practical');
  });

  return lastUsedComponents;
}

export async function POST(req: NextRequest) {
  try {
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({ error: "Missing GOOGLE_GENERATIVE_AI_API_KEY" }, { status: 500 });
    }

    const body = await req.json();
    const parsed = LearningHubSchema.safeParse(body);
    
    if (!parsed.success) {
      return Response.json({ 
        error: "Invalid payload", 
        details: parsed.error.flatten() 
      }, { status: 400 });
    }

    const { message, mode, certificate, conversationHistory } = parsed.data;

    const systemPrompt = buildSystemPrompt(certificate, mode);
    const userPrompt = buildUserPrompt(message, conversationHistory);

    // First, generate the main response
    const textResponse = await generateText({
      // Use fast, general model per foundational rules
      model: google("gemini-2.0-flash-exp"),
      system: systemPrompt,
      prompt: userPrompt,
      maxTokens: 1000,
    });

    // ALWAYS create components - this is MANDATORY for every response
    const shouldCreateComponents = true; // Force component creation for EVERY response

    let components = [];

    if (shouldCreateComponents) {
      try {
        const lastUsedComponents = getLastUsedComponents(conversationHistory);

        // Determine the best component type based on content analysis
        const messageContent = message.toLowerCase();
        const responseContent = textResponse.text.toLowerCase();

        // Detect explicit component type request
        const detectExplicitComponentRequest = (input: string): string | undefined => {
          const map: Record<string, string> = {
            'flashcard': 'flashcard',
            'flash card': 'flashcard',
            'concept explorer': 'concept_explorer',
            'concept_explorer': 'concept_explorer',
            'step by step': 'step_by_step',
            'step-by-step': 'step_by_step',
            'timeline': 'timeline',
            'comparison matrix': 'comparison_matrix',
            'comparison': 'comparison_matrix',
            'matrix': 'comparison_matrix',
            'practical': 'practical',
            'scenario': 'practical',
            'case study': 'practical',
            'question': 'question',
            'mcq': 'question',
            'multiple choice': 'question',
            'true or false': 'true_false',
            'true/false': 'true_false',
            'mini exam': 'mini_exam',
            'mini-assessment': 'mini_exam',
          };
          for (const key of Object.keys(map)) {
            if (input.includes(key)) return map[key];
          }
          return undefined;
        };

        let suggestedComponentType = detectExplicitComponentRequest(messageContent) || 'concept_explorer'; // Default for tutoring

        // ASSESSMENT MODE - Use testing components
        if (messageContent.includes('assess') || mode === 'assess') {
          suggestedComponentType = Math.random() > 0.5 ? 'question' : 'true_false';
        }
        // COMPARISON REQUESTS - Use comparison matrix
        else if (messageContent.includes('compare') || messageContent.includes('difference') || messageContent.includes('versus') ||
                 messageContent.includes('vs') || responseContent.includes('different') || responseContent.includes('contrast')) {
          suggestedComponentType = 'comparison_matrix';
        }
        // PROCESS/PROCEDURE REQUESTS - Use step-by-step
        else if (messageContent.includes('process') || messageContent.includes('step') || messageContent.includes('how to') ||
                 messageContent.includes('procedure') || responseContent.includes('steps') || responseContent.includes('process')) {
          suggestedComponentType = 'step_by_step';
        }
        // HISTORICAL/TIMELINE REQUESTS - Use timeline
        else if (messageContent.includes('history') || messageContent.includes('evolution') || messageContent.includes('development') ||
                 messageContent.includes('timeline') || responseContent.includes('over time') || responseContent.includes('chronolog')) {
          suggestedComponentType = 'timeline';
        }
        // SCENARIO/PRACTICAL REQUESTS - Use practical scenarios
        else if (messageContent.includes('scenario') || messageContent.includes('example') || messageContent.includes('case study') ||
                 messageContent.includes('real world') || responseContent.includes('scenario') || responseContent.includes('practical')) {
          suggestedComponentType = 'practical';
        }
        // SIMPLE DEFINITIONS - Use flashcards (but only for very specific cases)
        else if ((messageContent.includes('define') || messageContent.includes('what is')) &&
                 responseContent.length < 300 && !responseContent.includes('complex') && !responseContent.includes('multiple')) {
          suggestedComponentType = 'flashcard';
        }
        // COMPLEX EXPLANATIONS - Use concept explorer (default for most tutoring)
        else if (messageContent.includes('explain') || messageContent.includes('understand') || messageContent.includes('learn') ||
                 responseContent.includes('concept') || responseContent.includes('principle') || responseContent.length > 200) {
          suggestedComponentType = 'concept_explorer';
        }

        // Avoid recently used components ONLY if not explicitly requested by user
        if (!detectExplicitComponentRequest(messageContent) && lastUsedComponents.includes(suggestedComponentType)) {
          const alternatives = ['concept_explorer', 'step_by_step', 'timeline', 'comparison_matrix', 'flashcard', 'practical']
            .filter(type => !lastUsedComponents.includes(type));
          if (alternatives.length > 0) {
            suggestedComponentType = alternatives[Math.floor(Math.random() * alternatives.length)];
          }
        }

        const componentPrompt = `You are creating interactive learning components for ${certificate.name} certification.

CONTEXT:
User message: "${message}"
AI response: "${textResponse.text}"
Mode: ${mode || 'general'}
Certificate: ${certificate.name} (${certificate.domain})
Recently used components: ${lastUsedComponents.join(', ') || 'None'}
Suggested component type: ${suggestedComponentType}

ABSOLUTE REQUIREMENTS:
- MUST create exactly 1-2 components for this response
- MUST use the suggested component type: ${suggestedComponentType} (unless completely inappropriate)
- If the user explicitly requested a specific component type (e.g., "Create a Flashcard"), you MUST use that exact type.
- MUST provide complete, realistic data - NO placeholders or incomplete information
- MUST ensure components directly relate to the conversation content
- MUST make components engaging and educational
- STRONGLY PREFER tutoring components (concept_explorer, step_by_step, timeline, comparison_matrix, practical) over flashcards
- AVOID flashcards unless the content is truly just a simple definition
- PRIORITIZE interactive learning over memorization

COMPONENT SELECTION STRATEGY:

WHEN TO USE EACH COMPONENT:

1. concept_explorer: Use when explaining complex topics with multiple aspects
   - Example triggers: "explain GDPR", "what is data protection", "understand privacy principles"
   - Perfect for: Multi-layered concepts, fundamental principles, core topics

2. step_by_step: Use when teaching processes, procedures, or workflows
   - Example triggers: "how to conduct", "process of", "steps to", "procedure for"
   - Perfect for: DPIA process, compliance procedures, implementation steps

3. timeline: Use when discussing historical development, evolution, or sequences
   - Example triggers: "history of", "evolution", "development", "when did", chronological topics
   - Perfect for: Privacy law evolution, GDPR timeline, regulatory development

4. comparison_matrix: Use when contrasting 2+ concepts, roles, or approaches
   - Example triggers: "difference between", "compare", "vs", "contrast"
   - Perfect for: Controller vs Processor, different legal bases, various rights

5. flashcard: Use ONLY for simple definitions and basic terminology (AVOID overuse)
   - Example triggers: "define", "what is" (but ONLY for simple, short definitions)
   - Perfect for: Single-concept definitions, basic terminology
   - AVOID for: Complex concepts, multi-part explanations, anything that needs depth

6. practical: Use for real-world scenarios and applications
   - Example triggers: "scenario", "example", "real-world", "case study"
   - Perfect for: Compliance scenarios, practical applications

FOR ASSESSMENT MODE: question, true_false, mini_exam

EXACT COMPONENT DATA FORMATS (follow precisely):

concept_explorer: {
  "concept": "Main topic name",
  "definition": "Clear, comprehensive definition",
  "layers": [
    {
      "title": "Layer name",
      "description": "Detailed explanation",
      "examples": ["Example 1", "Example 2", "Example 3"],
      "realWorldApplication": "How this applies in practice",
      "icon": "Shield", // Options: Shield, Users, BookOpen, Eye, CheckCircle
      "color": "blue" // Options: blue, green, purple, amber
    }
  ],
  "keyTakeaways": ["Key point 1", "Key point 2", "Key point 3"]
}

step_by_step: {
  "topic": "Process/procedure name",
  "objective": "What the learner will achieve",
  "steps": [
    {
      "title": "Step name",
      "content": "Detailed step content",
      "tip": "Helpful tip (optional)",
      "checkpoint": "What to verify/understand"
    }
  ],
  "summary": "Key insights and takeaways"
}

timeline: {
  "topic": "Timeline subject",
  "subtitle": "Brief description",
  "events": [
    {
      "date": "Year or date",
      "title": "Event name",
      "description": "What happened",
      "impact": "Why it matters",
      "keyPoints": ["Point 1", "Point 2"]
    }
  ],
  "conclusion": "Overall significance"
}

comparison_matrix: {
  "title": "What is being compared",
  "subtitle": "Context/description",
  "items": [
    {"name": "Item 1", "description": "Brief description", "color": "blue"},
    {"name": "Item 2", "description": "Brief description", "color": "green"}
  ],
  "criteria": [
    {
      "name": "Criteria name",
      "description": "What this measures",
      "values": {"Item 1": "Value for item 1", "Item 2": "Value for item 2"}
    }
  ],
  "insights": ["Key insight 1", "Key insight 2"]
}

CRITICAL: Always provide complete, valid JSON data. Never use placeholder text.`;

        const componentResponse = await generateObject({
          model: google("gemini-2.0-flash-exp"),
          schema: z.object({
            components: z.array(ComponentSchema).min(1).max(2),
          }),
          prompt: componentPrompt,
        });

        components = componentResponse.object.components || [];

        // If no components were generated, create a meaningful fallback with the model (no placeholders)
        if (components.length === 0) {
          console.warn("No components generated, creating AI-backed fallback");
          const explicit = detectExplicitComponentRequest(messageContent);
          if (explicit === 'flashcard') {
            const fallbackFlash = await generateObject({
              model: google("gemini-2.0-flash-exp"),
              schema: z.object({
                flashcard: z.object({
                  front: z.string().min(3),
                  back: z.string().min(10),
                  category: z.string().optional(),
                })
              }),
              prompt: `Create a single high-quality flashcard for ${certificate.name} (domain: ${certificate.domain}) based on this content.\nContent:\n${textResponse.text}\nReturn JSON only matching the schema.`,
            });
            const f = fallbackFlash.object.flashcard;
            components = [{
              type: 'flashcard',
              data: {
                front: f.front,
                back: f.back,
                category: f.category || `${certificate.name}`,
              }
            }];
          } else if (mode === 'assess') {
            // Generate one focused MCQ from the last response
            const fallbackAssess = await generateObject({
              model: google("gemini-2.0-flash-exp"),
              schema: z.object({
                question: z.object({
                  question: z.string(),
                  options: z.array(z.string()).min(2),
                  correct: z.number().int().min(0),
                  explanation: z.string().min(10)
                })
              }),
              prompt: `Create a single high-quality multiple choice question based on this content for ${certificate.name} (domain: ${certificate.domain}).\nContent:\n${textResponse.text}\nReturn JSON only matching the schema.`,
            });
            const q = fallbackAssess.object.question;
            components = [{
              type: 'question',
              data: {
                question: q.question,
                options: q.options,
                correct: q.correct,
                explanation: q.explanation,
              }
            }];
          } else {
            const fallbackTutor = await generateObject({
              model: google("gemini-2.0-flash-exp"),
              schema: z.object({
                concept_explorer: z.object({
                  concept: z.string(),
                  definition: z.string(),
                  layers: z.array(z.object({
                    title: z.string(),
                    description: z.string(),
                    examples: z.array(z.string()).min(1),
                    realWorldApplication: z.string(),
                    icon: z.enum(["Shield", "Users", "BookOpen", "Eye", "CheckCircle"]).default("BookOpen"),
                    color: z.enum(["blue", "green", "purple", "amber"]).default("blue")
                  })).min(1),
                  keyTakeaways: z.array(z.string()).min(1)
                })
              }),
              prompt: `Create a concise but rich concept_explorer based on this content for ${certificate.name} (domain: ${certificate.domain}).\nContent:\n${textResponse.text}\nReturn JSON only matching the schema. Avoid placeholders.`,
            });
            const ce = fallbackTutor.object.concept_explorer;
            components = [{
              type: 'concept_explorer',
              data: ce,
            }];
          }
        }
      } catch (componentError) {
        console.error("Error generating components:", componentError);
        // Create a meaningful fallback component even on error by extracting a summary
        components = [{
          type: 'concept_explorer',
          data: {
            concept: `${certificate.name} – Key Concept`,
            definition: textResponse.text.split('\n')[0]?.slice(0, 240) || "Key concept overview",
            layers: [{
              title: "Core Understanding",
              description: textResponse.text.slice(0, 600),
              examples: ["Example application 1", "Example application 2"],
              realWorldApplication: "How this appears in real compliance practice",
              icon: "BookOpen",
              color: "blue"
            }],
            keyTakeaways: ["Understand the definition", "Know practical implications", "Remember typical pitfalls"]
          }
        }];
      }
    }

    return Response.json({
      content: textResponse.text,
      components: components,
      nextAction: mode === 'assess' ? 'Continue assessment' : mode === 'tutor' ? 'Continue learning' : 'Ask follow-up questions'
    });

  } catch (error) {
    console.error("Learning Hub API error:", error);
    return Response.json({ 
      error: "Failed to process request",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
