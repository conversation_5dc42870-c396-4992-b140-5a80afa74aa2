"use client";

import { useState, useEffect, useMemo } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { getUserExamAttempts, type ExamAttempt, type ExamMode } from "@/Services/examAttemptsService";
import {
  BarChart3,
  TrendingUp,
  Target,
  Award,
  Calendar,
  Clock,
  Trophy,
  BookOpen,
  Activity,
  Zap,
  Brain,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
  Flame,
  Timer,
  History,
  Eye,
  X,
  Grid3X3
} from "lucide-react";

interface PerformanceStats {
  totalAttempts: number;
  completedAttempts: number;
  averageScore: number;
  bestScore: number;
  totalTimeSpent: number;
  averageTimePerAttempt: number;
  improvementTrend: number;
  currentStreak: number;
  strongestTopics: string[];
  weakestTopics: string[];
  modePerformance: Record<ExamMode, { attempts: number; avgScore: number; bestScore: number }>;
  recentActivity: Array<{
    date: Date;
    type: 'exam' | 'practice' | 'study';
    description: string;
    score?: number;
    certificateId: string;
  }>;
  monthlyProgress: Array<{
    month: string;
    attempts: number;
    avgScore: number;
    improvement: number;
  }>;
}

export default function PerformanceManagement() {
  const params = useParams();
  const { user } = useUser();
  const certificateId = params?.framework as string || "";

  const [attempts, setAttempts] = useState<ExamAttempt[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'quarter' | 'all'>('month');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [selectedAttempt, setSelectedAttempt] = useState<ExamAttempt | null>(null);

  useEffect(() => {
    const fetchAttempts = async () => {
      if (!user?.uid) return;

      try {
        setLoading(true);
        // Get all attempts for this user across all certificates
        const allAttempts = await getUserExamAttempts(user.uid);
        setAttempts(allAttempts);
      } catch (error) {
        console.error("Error fetching attempts:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAttempts();
  }, [user?.uid]);

  const performanceStats: PerformanceStats = useMemo(() => {
    if (!attempts.length) {
      return {
        totalAttempts: 0,
        completedAttempts: 0,
        averageScore: 0,
        bestScore: 0,
        totalTimeSpent: 0,
        averageTimePerAttempt: 0,
        improvementTrend: 0,
        currentStreak: 0,
        strongestTopics: [],
        weakestTopics: [],
        modePerformance: {
          practice: { attempts: 0, avgScore: 0, bestScore: 0 },
          realExam: { attempts: 0, avgScore: 0, bestScore: 0 },
          questionBank: { attempts: 0, avgScore: 0, bestScore: 0 }
        },
        recentActivity: [],
        monthlyProgress: []
      };
    }

    const completedAttempts = attempts.filter(a => a.status === 'completed');
    const scores = completedAttempts.map(a => a.score || 0);
    const totalTimeSpent = completedAttempts.reduce((sum, a) => sum + (a.timeSpentSeconds || 0), 0);

    // Calculate improvement trend (last 5 vs previous 5 attempts)
    const recentScores = scores.slice(0, 5);
    const previousScores = scores.slice(5, 10);
    const recentAvg = recentScores.length ? recentScores.reduce((a, b) => a + b, 0) / recentScores.length : 0;
    const previousAvg = previousScores.length ? previousScores.reduce((a, b) => a + b, 0) / previousScores.length : 0;
    const improvementTrend = recentAvg - previousAvg;

    // Calculate current streak (consecutive passing attempts)
    let currentStreak = 0;
    for (const attempt of completedAttempts) {
      if ((attempt.score || 0) >= 70) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Mode performance analysis
    const modePerformance: Record<ExamMode, { attempts: number; avgScore: number; bestScore: number }> = {
      practice: { attempts: 0, avgScore: 0, bestScore: 0 },
      realExam: { attempts: 0, avgScore: 0, bestScore: 0 },
      questionBank: { attempts: 0, avgScore: 0, bestScore: 0 }
    };

    completedAttempts.forEach(attempt => {
      const mode = attempt.configuration.mode;
      const score = attempt.score || 0;

      modePerformance[mode].attempts++;
      modePerformance[mode].avgScore += score;
      modePerformance[mode].bestScore = Math.max(modePerformance[mode].bestScore, score);
    });

    // Calculate averages
    Object.keys(modePerformance).forEach(mode => {
      const modeKey = mode as ExamMode;
      if (modePerformance[modeKey].attempts > 0) {
        modePerformance[modeKey].avgScore = modePerformance[modeKey].avgScore / modePerformance[modeKey].attempts;
      }
    });

    // Recent activity
    const recentActivity = completedAttempts.slice(0, 10).map(attempt => ({
      date: attempt.completedAt instanceof Date ? attempt.completedAt : attempt.completedAt?.toDate() || new Date(),
      type: attempt.configuration.mode === 'realExam' ? 'exam' as const : 'practice' as const,
      description: `${attempt.configuration.mode === 'realExam' ? 'Real Exam' :
                    attempt.configuration.mode === 'practice' ? 'Practice Test' : 'Question Bank'} - ${attempt.certificateId}`,
      score: attempt.score,
      certificateId: attempt.certificateId
    }));

    // Monthly progress (last 6 months)
    const monthlyProgress = [];
    const now = new Date();
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthAttempts = completedAttempts.filter(attempt => {
        const attemptDate = attempt.completedAt instanceof Date ? attempt.completedAt : attempt.completedAt?.toDate();
        return attemptDate &&
               attemptDate.getMonth() === monthDate.getMonth() &&
               attemptDate.getFullYear() === monthDate.getFullYear();
      });

      const monthScores = monthAttempts.map(a => a.score || 0);
      const avgScore = monthScores.length ? monthScores.reduce((a, b) => a + b, 0) / monthScores.length : 0;

      // Calculate improvement from previous month
      const prevMonthDate = new Date(monthDate.getFullYear(), monthDate.getMonth() - 1, 1);
      const prevMonthAttempts = completedAttempts.filter(attempt => {
        const attemptDate = attempt.completedAt instanceof Date ? attempt.completedAt : attempt.completedAt?.toDate();
        return attemptDate &&
               attemptDate.getMonth() === prevMonthDate.getMonth() &&
               attemptDate.getFullYear() === prevMonthDate.getFullYear();
      });
      const prevAvgScore = prevMonthAttempts.length ?
        prevMonthAttempts.reduce((sum, a) => sum + (a.score || 0), 0) / prevMonthAttempts.length : 0;

      monthlyProgress.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        attempts: monthAttempts.length,
        avgScore,
        improvement: avgScore - prevAvgScore
      });
    }

    return {
      totalAttempts: attempts.length,
      completedAttempts: completedAttempts.length,
      averageScore: scores.length ? scores.reduce((a, b) => a + b, 0) / scores.length : 0,
      bestScore: Math.max(...scores, 0),
      totalTimeSpent,
      averageTimePerAttempt: completedAttempts.length ? totalTimeSpent / completedAttempts.length : 0,
      improvementTrend,
      currentStreak,
      strongestTopics: [], // TODO: Implement topic analysis
      weakestTopics: [], // TODO: Implement topic analysis
      modePerformance,
      recentActivity,
      monthlyProgress
    };
  }, [attempts]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-accent";
    if (score >= 70) return "text-lime-green";
    if (score >= 60) return "text-yellow-600";
    return "text-red-500";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-accent/10";
    if (score >= 70) return "bg-lime-green/10";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  if (loading) {
    return (
      <div className="w-full flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-grey">Loading performance data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-charcoal mb-2">Performance Management</h2>
        <p className="text-grey">Track your overall learning progress and performance across all certificates</p>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Average Score */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary/10 rounded-xl">
              <Trophy className="h-6 w-6 text-primary" />
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              performanceStats.improvementTrend > 0 ? 'bg-accent/20 text-accent' :
              performanceStats.improvementTrend < 0 ? 'bg-red-100 text-red-600' : 'bg-grey/20 text-grey'
            }`}>
              {performanceStats.improvementTrend > 0 ? '+' : ''}{performanceStats.improvementTrend.toFixed(1)}%
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">{performanceStats.averageScore.toFixed(1)}%</h3>
            <p className="text-sm font-medium text-charcoal">Average Score</p>
            <p className="text-xs text-grey">Across {performanceStats.completedAttempts} completed attempts</p>
          </div>
        </div>

        {/* Best Score */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent/10 rounded-xl">
              <Star className="h-6 w-6 text-accent" />
            </div>
            <div className="p-2 bg-accent/20 rounded-full">
              <Award className="h-4 w-4 text-accent" />
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">{performanceStats.bestScore}%</h3>
            <p className="text-sm font-medium text-charcoal">Best Score</p>
            <p className="text-xs text-grey">Personal record achievement</p>
          </div>
        </div>

        {/* Passed Attempts */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent/10 rounded-xl">
              <CheckCircle className="h-6 w-6 text-accent" />
            </div>
            <div className="p-2 bg-accent/20 rounded-full">
              <Trophy className="h-4 w-4 text-accent" />
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">
              {attempts.filter(a => a.status === 'completed' && (a.score || 0) >= 70).length}
            </h3>
            <p className="text-sm font-medium text-charcoal">Passed Attempts</p>
            <p className="text-xs text-grey">Score ≥ 70%</p>
          </div>
        </div>

        {/* Failed Attempts */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-red-100 rounded-xl">
              <XCircle className="h-6 w-6 text-red-500" />
            </div>
            <div className="p-2 bg-red-100 rounded-full">
              <AlertCircle className="h-4 w-4 text-red-500" />
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">
              {attempts.filter(a => a.status === 'completed' && (a.score || 0) < 70).length}
            </h3>
            <p className="text-sm font-medium text-charcoal">Failed Attempts</p>
            <p className="text-xs text-grey">Score &lt; 70%</p>
          </div>
        </div>
      </div>

      {/* Performance by Mode - Enhanced UI */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-charcoal flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BarChart3 className="h-6 w-6 text-primary" />
            </div>
            Performance by Mode
          </h3>
          <button
            onClick={() => setIsSidebarOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-deep transition-colors"
          >
            <History className="h-4 w-4" />
            View History
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Object.entries(performanceStats.modePerformance).map(([mode, stats]) => {
            const modeLabels = {
              practice: {
                label: 'Practice Tests',
                icon: Brain,
                color: 'accent',
                bgGradient: 'from-accent/10 to-accent/20',
                borderColor: 'border-accent/30'
              },
              realExam: {
                label: 'Real Exams',
                icon: Trophy,
                color: 'primary',
                bgGradient: 'from-primary/10 to-primary/20',
                borderColor: 'border-primary/30'
              },
              questionBank: {
                label: 'Question Bank',
                icon: BookOpen,
                color: 'lime-green',
                bgGradient: 'from-lime-green/10 to-lime-green/20',
                borderColor: 'border-lime-green/30'
              }
            };

            const modeInfo = modeLabels[mode as ExamMode];
            const Icon = modeInfo.icon;

            if (stats.attempts === 0) {
              return (
                <div key={mode} className={`bg-gradient-to-br ${modeInfo.bgGradient} rounded-2xl p-6 border ${modeInfo.borderColor} opacity-50`}>
                  <div className="text-center">
                    <div className={`p-4 bg-${modeInfo.color}/10 rounded-2xl inline-flex mb-4`}>
                      <Icon className={`h-8 w-8 text-${modeInfo.color}`} />
                    </div>
                    <h4 className="text-lg font-bold text-charcoal mb-2">{modeInfo.label}</h4>
                    <p className="text-grey">No attempts yet</p>
                  </div>
                </div>
              );
            }

            const accuracy = stats.avgScore;
            const passRate = stats.attempts > 0 ? ((stats.attempts * (accuracy / 100)) / stats.attempts) * 100 : 0;

            return (
              <div key={mode} className={`bg-gradient-to-br ${modeInfo.bgGradient} rounded-2xl p-6 border ${modeInfo.borderColor}`}>
                <div className="text-center">
                  <div className={`p-4 bg-${modeInfo.color}/10 rounded-2xl inline-flex mb-4`}>
                    <Icon className={`h-8 w-8 text-${modeInfo.color}`} />
                  </div>
                  <h4 className="text-lg font-bold text-charcoal mb-2">{modeInfo.label}</h4>
                  <p className={`text-4xl font-bold text-${modeInfo.color} mb-2`}>{accuracy.toFixed(1)}%</p>
                  <p className="text-grey font-medium mb-4">{stats.attempts} attempts</p>

                  {/* Progress Circle */}
                  <div className="relative w-20 h-20 mx-auto mb-4">
                    <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="35"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        className="text-gray-200"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="35"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        strokeDasharray={`${accuracy * 2.2} 220`}
                        className={`text-${modeInfo.color}`}
                        strokeLinecap="round"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className={`text-sm font-bold text-${modeInfo.color}`}>{accuracy.toFixed(0)}%</span>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-grey">Best Score:</span>
                      <span className="font-medium text-charcoal">{stats.bestScore}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-grey">Passed:</span>
                      <span className="font-medium text-charcoal">
                        {attempts.filter(a => a.configuration.mode === mode && a.status === 'completed' && (a.score || 0) >= 70).length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Category Performance Matrix */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-charcoal flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Grid3X3 className="h-6 w-6 text-primary" />
            </div>
            Performance by Category
          </h3>
        </div>

        {/* Category Matrix */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          {performanceStats.completedAttempts === 0 ? (
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-grey mx-auto mb-4" />
              <p className="text-lg font-medium text-charcoal mb-2">No Performance Data Yet</p>
              <p className="text-grey">Complete some exams to see your category performance analysis</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {/* Mock categories - In real implementation, this would come from analyzing attempt answers */}
              {[
                { name: 'Security', score: 85, attempts: 12, strongest: true },
                { name: 'Networking', score: 78, attempts: 8, strongest: false },
                { name: 'Storage', score: 92, attempts: 6, strongest: false },
                { name: 'Compute', score: 71, attempts: 10, strongest: false },
                { name: 'Database', score: 88, attempts: 7, strongest: false },
                { name: 'Monitoring', score: 65, attempts: 5, strongest: false },
                { name: 'Identity', score: 82, attempts: 9, strongest: false },
                { name: 'Cost Optimization', score: 76, attempts: 4, strongest: false }
              ].map((category, index) => (
                <div
                  key={category.name}
                  className={`relative p-4 rounded-xl border transition-all hover:shadow-md ${
                    category.strongest
                      ? 'bg-gradient-to-br from-accent/20 to-accent/10 border-accent/40'
                      : category.score >= 80
                        ? 'bg-gradient-to-br from-primary/10 to-primary/5 border-primary/20'
                        : category.score >= 70
                          ? 'bg-gradient-to-br from-lime-green/10 to-lime-green/5 border-lime-green/20'
                          : 'bg-gradient-to-br from-red-50 to-red-25 border-red-200'
                  }`}
                >
                  {category.strongest && (
                    <div className="absolute -top-2 -right-2">
                      <div className="bg-accent text-white text-xs px-2 py-1 rounded-full font-medium">
                        Strongest
                      </div>
                    </div>
                  )}

                  <div className="text-center">
                    <h4 className="font-semibold text-charcoal mb-2">{category.name}</h4>
                    <div className={`text-2xl font-bold mb-1 ${
                      category.strongest
                        ? 'text-accent'
                        : category.score >= 80
                          ? 'text-primary'
                          : category.score >= 70
                            ? 'text-lime-green'
                            : 'text-red-500'
                    }`}>
                      {category.score}%
                    </div>
                    <p className="text-xs text-grey">{category.attempts} questions</p>

                    {/* Mini progress bar */}
                    <div className="w-full bg-gray-200 rounded-full h-1.5 mt-3">
                      <div
                        className={`h-1.5 rounded-full transition-all duration-500 ${
                          category.strongest
                            ? 'bg-accent'
                            : category.score >= 80
                              ? 'bg-primary'
                              : category.score >= 70
                                ? 'bg-lime-green'
                                : 'bg-red-500'
                        }`}
                        style={{ width: `${category.score}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Sidebar for Attempt History */}
      {isSidebarOpen && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black/50" onClick={() => setIsSidebarOpen(false)}></div>
          <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-charcoal">Attempt History</h3>
                <button
                  onClick={() => setIsSidebarOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="h-5 w-5 text-grey" />
                </button>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-4">
                  {attempts.filter(a => a.status === 'completed').length === 0 ? (
                    <div className="text-center py-12">
                      <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
                      <p className="text-grey">No completed attempts found</p>
                    </div>
                  ) : (
                    attempts
                      .filter(a => a.status === 'completed')
                      .sort((a, b) => {
                        const aTime = a.completedAt instanceof Date ? a.completedAt.getTime() : a.completedAt?.toDate().getTime() || 0;
                        const bTime = b.completedAt instanceof Date ? b.completedAt.getTime() : b.completedAt?.toDate().getTime() || 0;
                        return bTime - aTime;
                      })
                      .map((attempt) => (
                        <div
                          key={attempt.id}
                          className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => {
                            setSelectedAttempt(attempt);
                            // Here you would navigate to the performance report
                            // For now, we'll just show an alert
                            alert(`Would navigate to performance report for attempt: ${attempt.id}`);
                          }}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${
                                attempt.configuration.mode === 'realExam' ? 'bg-primary/10' :
                                attempt.configuration.mode === 'practice' ? 'bg-accent/10' : 'bg-lime-green/10'
                              }`}>
                                {attempt.configuration.mode === 'realExam' ? (
                                  <Trophy className={`h-4 w-4 ${
                                    attempt.configuration.mode === 'realExam' ? 'text-primary' :
                                    attempt.configuration.mode === 'practice' ? 'text-accent' : 'text-lime-green'
                                  }`} />
                                ) : attempt.configuration.mode === 'practice' ? (
                                  <Brain className={`h-4 w-4 ${
                                    attempt.configuration.mode === 'realExam' ? 'text-primary' :
                                    attempt.configuration.mode === 'practice' ? 'text-accent' : 'text-lime-green'
                                  }`} />
                                ) : (
                                  <BookOpen className={`h-4 w-4 ${
                                    attempt.configuration.mode === 'realExam' ? 'text-primary' :
                                    attempt.configuration.mode === 'practice' ? 'text-accent' : 'text-lime-green'
                                  }`} />
                                )}
                              </div>
                              <div>
                                <p className="font-medium text-charcoal">{attempt.id}</p>
                                <p className="text-xs text-grey">
                                  {attempt.configuration.mode === 'realExam' ? 'Real Exam' :
                                   attempt.configuration.mode === 'practice' ? 'Practice Test' : 'Question Bank'}
                                </p>
                              </div>
                            </div>
                            <Eye className="h-4 w-4 text-grey" />
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                                (attempt.score || 0) >= 70 ? 'bg-accent/20 text-accent' : 'bg-red-100 text-red-600'
                              }`}>
                                {(attempt.score || 0) >= 70 ? 'Passed' : 'Failed'}
                              </div>
                              <span className={`text-lg font-bold ${getScoreColor(attempt.score || 0)}`}>
                                {attempt.score || 0}%
                              </span>
                            </div>
                            <div className="text-right">
                              <p className="text-xs text-grey">
                                {attempt.completedAt instanceof Date
                                  ? attempt.completedAt.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                                  : attempt.completedAt?.toDate().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                                }
                              </p>
                              <p className="text-xs text-grey">
                                {attempt.correctAnswers || 0}/{attempt.totalQuestions}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
