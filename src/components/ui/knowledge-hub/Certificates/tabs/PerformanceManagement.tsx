"use client";

import { useState, useEffect, useMemo } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { getUserExamAttempts, type ExamAttempt, type ExamMode } from "@/Services/examAttemptsService";
import {
  BarChart3,
  TrendingUp,
  Target,
  Award,
  Calendar,
  Clock,
  Trophy,
  BookOpen,
  Activity,
  Zap,
  Brain,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
  Flame,
  Timer
} from "lucide-react";

interface PerformanceStats {
  totalAttempts: number;
  completedAttempts: number;
  averageScore: number;
  bestScore: number;
  totalTimeSpent: number;
  averageTimePerAttempt: number;
  improvementTrend: number;
  currentStreak: number;
  strongestTopics: string[];
  weakestTopics: string[];
  modePerformance: Record<ExamMode, { attempts: number; avgScore: number; bestScore: number }>;
  recentActivity: Array<{
    date: Date;
    type: 'exam' | 'practice' | 'study';
    description: string;
    score?: number;
    certificateId: string;
  }>;
  monthlyProgress: Array<{
    month: string;
    attempts: number;
    avgScore: number;
    improvement: number;
  }>;
}

export default function PerformanceManagement() {
  const params = useParams();
  const { user } = useUser();
  const certificateId = params?.framework as string || "";

  const [attempts, setAttempts] = useState<ExamAttempt[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'quarter' | 'all'>('month');

  useEffect(() => {
    const fetchAttempts = async () => {
      if (!user?.uid) return;

      try {
        setLoading(true);
        // Get all attempts for this user across all certificates
        const allAttempts = await getUserExamAttempts(user.uid);
        setAttempts(allAttempts);
      } catch (error) {
        console.error("Error fetching attempts:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAttempts();
  }, [user?.uid]);

  const performanceStats: PerformanceStats = useMemo(() => {
    if (!attempts.length) {
      return {
        totalAttempts: 0,
        completedAttempts: 0,
        averageScore: 0,
        bestScore: 0,
        totalTimeSpent: 0,
        averageTimePerAttempt: 0,
        improvementTrend: 0,
        currentStreak: 0,
        strongestTopics: [],
        weakestTopics: [],
        modePerformance: {
          practice: { attempts: 0, avgScore: 0, bestScore: 0 },
          realExam: { attempts: 0, avgScore: 0, bestScore: 0 },
          questionBank: { attempts: 0, avgScore: 0, bestScore: 0 }
        },
        recentActivity: [],
        monthlyProgress: []
      };
    }

    const completedAttempts = attempts.filter(a => a.status === 'completed');
    const scores = completedAttempts.map(a => a.score || 0);
    const totalTimeSpent = completedAttempts.reduce((sum, a) => sum + (a.timeSpentSeconds || 0), 0);

    // Calculate improvement trend (last 5 vs previous 5 attempts)
    const recentScores = scores.slice(0, 5);
    const previousScores = scores.slice(5, 10);
    const recentAvg = recentScores.length ? recentScores.reduce((a, b) => a + b, 0) / recentScores.length : 0;
    const previousAvg = previousScores.length ? previousScores.reduce((a, b) => a + b, 0) / previousScores.length : 0;
    const improvementTrend = recentAvg - previousAvg;

    // Calculate current streak (consecutive passing attempts)
    let currentStreak = 0;
    for (const attempt of completedAttempts) {
      if ((attempt.score || 0) >= 70) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Mode performance analysis
    const modePerformance: Record<ExamMode, { attempts: number; avgScore: number; bestScore: number }> = {
      practice: { attempts: 0, avgScore: 0, bestScore: 0 },
      realExam: { attempts: 0, avgScore: 0, bestScore: 0 },
      questionBank: { attempts: 0, avgScore: 0, bestScore: 0 }
    };

    completedAttempts.forEach(attempt => {
      const mode = attempt.configuration.mode;
      const score = attempt.score || 0;

      modePerformance[mode].attempts++;
      modePerformance[mode].avgScore += score;
      modePerformance[mode].bestScore = Math.max(modePerformance[mode].bestScore, score);
    });

    // Calculate averages
    Object.keys(modePerformance).forEach(mode => {
      const modeKey = mode as ExamMode;
      if (modePerformance[modeKey].attempts > 0) {
        modePerformance[modeKey].avgScore = modePerformance[modeKey].avgScore / modePerformance[modeKey].attempts;
      }
    });

    // Recent activity
    const recentActivity = completedAttempts.slice(0, 10).map(attempt => ({
      date: attempt.completedAt instanceof Date ? attempt.completedAt : attempt.completedAt?.toDate() || new Date(),
      type: attempt.configuration.mode === 'realExam' ? 'exam' as const : 'practice' as const,
      description: `${attempt.configuration.mode === 'realExam' ? 'Real Exam' :
                    attempt.configuration.mode === 'practice' ? 'Practice Test' : 'Question Bank'} - ${attempt.certificateId}`,
      score: attempt.score,
      certificateId: attempt.certificateId
    }));

    // Monthly progress (last 6 months)
    const monthlyProgress = [];
    const now = new Date();
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthAttempts = completedAttempts.filter(attempt => {
        const attemptDate = attempt.completedAt instanceof Date ? attempt.completedAt : attempt.completedAt?.toDate();
        return attemptDate &&
               attemptDate.getMonth() === monthDate.getMonth() &&
               attemptDate.getFullYear() === monthDate.getFullYear();
      });

      const monthScores = monthAttempts.map(a => a.score || 0);
      const avgScore = monthScores.length ? monthScores.reduce((a, b) => a + b, 0) / monthScores.length : 0;

      // Calculate improvement from previous month
      const prevMonthDate = new Date(monthDate.getFullYear(), monthDate.getMonth() - 1, 1);
      const prevMonthAttempts = completedAttempts.filter(attempt => {
        const attemptDate = attempt.completedAt instanceof Date ? attempt.completedAt : attempt.completedAt?.toDate();
        return attemptDate &&
               attemptDate.getMonth() === prevMonthDate.getMonth() &&
               attemptDate.getFullYear() === prevMonthDate.getFullYear();
      });
      const prevAvgScore = prevMonthAttempts.length ?
        prevMonthAttempts.reduce((sum, a) => sum + (a.score || 0), 0) / prevMonthAttempts.length : 0;

      monthlyProgress.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        attempts: monthAttempts.length,
        avgScore,
        improvement: avgScore - prevAvgScore
      });
    }

    return {
      totalAttempts: attempts.length,
      completedAttempts: completedAttempts.length,
      averageScore: scores.length ? scores.reduce((a, b) => a + b, 0) / scores.length : 0,
      bestScore: Math.max(...scores, 0),
      totalTimeSpent,
      averageTimePerAttempt: completedAttempts.length ? totalTimeSpent / completedAttempts.length : 0,
      improvementTrend,
      currentStreak,
      strongestTopics: [], // TODO: Implement topic analysis
      weakestTopics: [], // TODO: Implement topic analysis
      modePerformance,
      recentActivity,
      monthlyProgress
    };
  }, [attempts]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-accent";
    if (score >= 70) return "text-lime-green";
    if (score >= 60) return "text-yellow-600";
    return "text-red-500";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-accent/10";
    if (score >= 70) return "bg-lime-green/10";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  if (loading) {
    return (
      <div className="w-full flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-grey">Loading performance data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-charcoal mb-2">Performance Management</h2>
        <p className="text-grey">Track your overall learning progress and performance across all certificates</p>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Overall Score */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary/10 rounded-xl">
              <Trophy className="h-6 w-6 text-primary" />
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              performanceStats.improvementTrend > 0 ? 'bg-accent/20 text-accent' :
              performanceStats.improvementTrend < 0 ? 'bg-red-100 text-red-600' : 'bg-grey/20 text-grey'
            }`}>
              {performanceStats.improvementTrend > 0 ? '+' : ''}{performanceStats.improvementTrend.toFixed(1)}%
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">{performanceStats.averageScore.toFixed(1)}%</h3>
            <p className="text-sm font-medium text-charcoal">Average Score</p>
            <p className="text-xs text-grey">Across {performanceStats.completedAttempts} completed attempts</p>
          </div>
        </div>

        {/* Best Score */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent/10 rounded-xl">
              <Star className="h-6 w-6 text-accent" />
            </div>
            <div className="p-2 bg-accent/20 rounded-full">
              <Award className="h-4 w-4 text-accent" />
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">{performanceStats.bestScore}%</h3>
            <p className="text-sm font-medium text-charcoal">Best Score</p>
            <p className="text-xs text-grey">Personal record achievement</p>
          </div>
        </div>

        {/* Current Streak */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-bright-green/10 rounded-xl">
              <Flame className="h-6 w-6 text-bright-green" />
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              performanceStats.currentStreak > 0 ? 'bg-bright-green/20 text-bright-green' : 'bg-grey/20 text-grey'
            }`}>
              {performanceStats.currentStreak > 0 ? 'Active' : 'Inactive'}
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">{performanceStats.currentStreak}</h3>
            <p className="text-sm font-medium text-charcoal">Passing Streak</p>
            <p className="text-xs text-grey">Consecutive passing attempts</p>
          </div>
        </div>

        {/* Study Time */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-lime-green/10 rounded-xl">
              <Clock className="h-6 w-6 text-lime-green" />
            </div>
            <div className="p-2 bg-lime-green/20 rounded-full">
              <Timer className="h-4 w-4 text-lime-green" />
            </div>
          </div>
          <div className="space-y-1">
            <h3 className="text-3xl font-bold text-charcoal">{formatTime(performanceStats.totalTimeSpent)}</h3>
            <p className="text-sm font-medium text-charcoal">Total Study Time</p>
            <p className="text-xs text-grey">Avg: {formatTime(performanceStats.averageTimePerAttempt)} per attempt</p>
          </div>
        </div>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Monthly Progress Chart */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-charcoal flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <TrendingUp className="h-5 w-5 text-primary" />
              </div>
              Monthly Progress
            </h3>
          </div>

          <div className="space-y-4">
            {performanceStats.monthlyProgress.map((month, index) => (
              <div key={month.month} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-charcoal">{month.month}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-grey">{month.attempts} attempts</span>
                    <span className={`text-sm font-medium ${getScoreColor(month.avgScore)}`}>
                      {month.avgScore.toFixed(1)}%
                    </span>
                    {month.improvement !== 0 && (
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        month.improvement > 0 ? 'bg-accent/20 text-accent' : 'bg-red-100 text-red-600'
                      }`}>
                        {month.improvement > 0 ? '+' : ''}{month.improvement.toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-500 ${
                      month.avgScore >= 80 ? 'bg-gradient-to-r from-accent to-primary' :
                      month.avgScore >= 70 ? 'bg-gradient-to-r from-lime-green to-accent' :
                      month.avgScore >= 60 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                      'bg-gradient-to-r from-red-400 to-red-500'
                    }`}
                    style={{
                      width: `${Math.max(5, month.avgScore)}%`,
                      animationDelay: `${index * 100}ms`
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Mode Performance */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-charcoal flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <BarChart3 className="h-5 w-5 text-primary" />
              </div>
              Performance by Mode
            </h3>
          </div>

          <div className="space-y-6">
            {Object.entries(performanceStats.modePerformance).map(([mode, stats]) => {
              const modeLabels = {
                practice: { label: 'Practice Tests', icon: Brain, color: 'accent' },
                realExam: { label: 'Real Exams', icon: Trophy, color: 'primary' },
                questionBank: { label: 'Question Bank', icon: BookOpen, color: 'lime-green' }
              };

              const modeInfo = modeLabels[mode as ExamMode];
              const Icon = modeInfo.icon;

              if (stats.attempts === 0) return null;

              return (
                <div key={mode} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 bg-${modeInfo.color}/10 rounded-lg`}>
                        <Icon className={`h-4 w-4 text-${modeInfo.color}`} />
                      </div>
                      <div>
                        <p className="font-medium text-charcoal">{modeInfo.label}</p>
                        <p className="text-xs text-grey">{stats.attempts} attempts</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-lg font-bold ${getScoreColor(stats.avgScore)}`}>
                        {stats.avgScore.toFixed(1)}%
                      </p>
                      <p className="text-xs text-grey">Best: {stats.bestScore}%</p>
                    </div>
                  </div>

                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 bg-${modeInfo.color}`}
                      style={{ width: `${Math.max(5, stats.avgScore)}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Recent Activity & Performance Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* Recent Activity */}
        <div className="lg:col-span-2 bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-charcoal flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Activity className="h-5 w-5 text-primary" />
              </div>
              Recent Activity
            </h3>
          </div>

          <div className="space-y-4">
            {performanceStats.recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
                <p className="text-grey">No recent activity found</p>
                <p className="text-sm text-grey">Start taking exams to see your activity here</p>
              </div>
            ) : (
              performanceStats.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center gap-4">
                    <div className={`p-2 rounded-lg ${
                      activity.type === 'exam' ? 'bg-primary/10' : 'bg-accent/10'
                    }`}>
                      {activity.type === 'exam' ? (
                        <Trophy className={`h-4 w-4 ${activity.type === 'exam' ? 'text-primary' : 'text-accent'}`} />
                      ) : (
                        <Brain className={`h-4 w-4 ${activity.type === 'exam' ? 'text-primary' : 'text-accent'}`} />
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-charcoal">{activity.description}</p>
                      <p className="text-xs text-grey">
                        {activity.date.toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {activity.score !== undefined && (
                      <div className="flex items-center gap-2">
                        <span className={`text-sm font-medium ${getScoreColor(activity.score)}`}>
                          {activity.score}%
                        </span>
                        {activity.score >= 70 ? (
                          <CheckCircle className="h-4 w-4 text-accent" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="bg-gradient-to-br from-white to-muted/30 rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-charcoal flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Zap className="h-5 w-5 text-primary" />
              </div>
              Insights
            </h3>
          </div>

          <div className="space-y-6">
            {/* Performance Status */}
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-charcoal">Performance Status</h4>
              {performanceStats.averageScore >= 80 ? (
                <div className="flex items-center gap-3 p-3 bg-accent/10 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-accent flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-accent">Excellent Performance</p>
                    <p className="text-xs text-grey">You're consistently scoring above 80%</p>
                  </div>
                </div>
              ) : performanceStats.averageScore >= 70 ? (
                <div className="flex items-center gap-3 p-3 bg-lime-green/10 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-lime-green flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-lime-green">Good Performance</p>
                    <p className="text-xs text-grey">You're passing consistently</p>
                  </div>
                </div>
              ) : performanceStats.averageScore >= 60 ? (
                <div className="flex items-center gap-3 p-3 bg-yellow-100 rounded-lg">
                  <AlertCircle className="h-5 w-5 text-yellow-600 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-700">Needs Improvement</p>
                    <p className="text-xs text-grey">Focus on weak areas</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-3 p-3 bg-red-100 rounded-lg">
                  <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-red-600">Requires Focus</p>
                    <p className="text-xs text-grey">Consider additional study time</p>
                  </div>
                </div>
              )}
            </div>

            {/* Improvement Trend */}
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-charcoal">Improvement Trend</h4>
              {performanceStats.improvementTrend > 5 ? (
                <div className="flex items-center gap-3 p-3 bg-accent/10 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-accent flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-accent">Strong Improvement</p>
                    <p className="text-xs text-grey">+{performanceStats.improvementTrend.toFixed(1)}% recent trend</p>
                  </div>
                </div>
              ) : performanceStats.improvementTrend > 0 ? (
                <div className="flex items-center gap-3 p-3 bg-lime-green/10 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-lime-green flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-lime-green">Gradual Improvement</p>
                    <p className="text-xs text-grey">+{performanceStats.improvementTrend.toFixed(1)}% recent trend</p>
                  </div>
                </div>
              ) : performanceStats.improvementTrend < -5 ? (
                <div className="flex items-center gap-3 p-3 bg-red-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-red-500 flex-shrink-0 rotate-180" />
                  <div>
                    <p className="text-sm font-medium text-red-600">Declining Trend</p>
                    <p className="text-xs text-grey">{performanceStats.improvementTrend.toFixed(1)}% recent trend</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-3 p-3 bg-gray-100 rounded-lg">
                  <Target className="h-5 w-5 text-grey flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-grey">Stable Performance</p>
                    <p className="text-xs text-grey">Consistent results</p>
                  </div>
                </div>
              )}
            </div>

            {/* Study Recommendations */}
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-charcoal">Recommendations</h4>
              <div className="space-y-2">
                {performanceStats.averageScore < 70 && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-800">Focus on Practice Tests</p>
                    <p className="text-xs text-blue-600">Increase practice frequency to improve scores</p>
                  </div>
                )}
                {performanceStats.currentStreak === 0 && performanceStats.completedAttempts > 0 && (
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <p className="text-sm font-medium text-orange-800">Build Consistency</p>
                    <p className="text-xs text-orange-600">Aim for consecutive passing attempts</p>
                  </div>
                )}
                {performanceStats.completedAttempts < 5 && (
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <p className="text-sm font-medium text-purple-800">Take More Assessments</p>
                    <p className="text-xs text-purple-600">More data will provide better insights</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats Summary */}
      {performanceStats.completedAttempts > 0 && (
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-6 border border-primary/10">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <p className="text-2xl font-bold text-primary">{performanceStats.totalAttempts}</p>
              <p className="text-sm text-grey">Total Attempts</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-accent">{performanceStats.completedAttempts}</p>
              <p className="text-sm text-grey">Completed</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-lime-green">
                {Math.round((performanceStats.completedAttempts / performanceStats.totalAttempts) * 100)}%
              </p>
              <p className="text-sm text-grey">Completion Rate</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-bright-green">
                {performanceStats.completedAttempts > 0 ?
                  Math.round((performanceStats.recentActivity.filter(a => (a.score || 0) >= 70).length / Math.min(performanceStats.completedAttempts, 10)) * 100) : 0}%
              </p>
              <p className="text-sm text-grey">Recent Pass Rate</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
