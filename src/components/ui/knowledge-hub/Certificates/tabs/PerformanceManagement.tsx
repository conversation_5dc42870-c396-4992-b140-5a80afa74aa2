"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { BarChart3, TrendingUp, Target, Award, Calendar, Users } from "lucide-react";

export default function PerformanceManagement() {
  const params = useParams();
  const certificateId = params?.framework as string || "";

  // Mock data for performance metrics
  const performanceMetrics = [
    {
      title: "Overall Progress",
      value: "78%",
      change: "+12%",
      trend: "up",
      icon: TrendingUp,
      description: "Completion rate across all modules"
    },
    {
      title: "Average Score",
      value: "85.2",
      change: "+5.3",
      trend: "up", 
      icon: Target,
      description: "Average exam performance"
    },
    {
      title: "Certificates Earned",
      value: "3",
      change: "+1",
      trend: "up",
      icon: Award,
      description: "Total certifications achieved"
    },
    {
      title: "Study Streak",
      value: "12 days",
      change: "+2",
      trend: "up",
      icon: Calendar,
      description: "Consecutive days of activity"
    }
  ];

  const recentActivities = [
    { date: "2024-01-15", activity: "Completed AWS Solutions Architect Practice Exam", score: "92%" },
    { date: "2024-01-14", activity: "Studied EC2 and VPC modules", score: "88%" },
    { date: "2024-01-13", activity: "Completed Security Best Practices quiz", score: "95%" },
    { date: "2024-01-12", activity: "Reviewed IAM policies and roles", score: "90%" }
  ];

  return (
    <div className="w-full">
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-charcoal mb-2">Performance Management</h2>
        <p className="text-grey">Track your learning progress and performance metrics</p>
      </div>

      {/* Performance Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {performanceMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Icon className="h-5 w-5 text-primary" />
                </div>
                <span className={`text-sm font-medium ${
                  metric.trend === 'up' ? 'text-accent' : 'text-red-500'
                }`}>
                  {metric.change}
                </span>
              </div>
              <div className="space-y-1">
                <h3 className="text-2xl font-bold text-charcoal">{metric.value}</h3>
                <p className="text-sm font-medium text-charcoal">{metric.title}</p>
                <p className="text-xs text-grey">{metric.description}</p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Performance Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Progress Chart */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-charcoal">Learning Progress</h3>
            <BarChart3 className="h-5 w-5 text-grey" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-grey">Module Completion</span>
              <span className="text-sm font-medium text-charcoal">78%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: '78%' }}></div>
            </div>
          </div>
        </div>

        {/* Performance Trends */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-charcoal">Performance Trends</h3>
            <TrendingUp className="h-5 w-5 text-grey" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-grey">This Month</span>
              <span className="text-sm font-medium text-accent">+15% improvement</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-grey">Last 7 Days</span>
              <span className="text-sm font-medium text-accent">+8% improvement</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-charcoal">Recent Activities</h3>
          <Users className="h-5 w-5 text-grey" />
        </div>
        <div className="space-y-4">
          {recentActivities.map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
              <div className="flex-1">
                <p className="text-sm font-medium text-charcoal">{activity.activity}</p>
                <p className="text-xs text-grey">{activity.date}</p>
              </div>
              <div className="text-right">
                <span className="text-sm font-medium text-accent">{activity.score}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
