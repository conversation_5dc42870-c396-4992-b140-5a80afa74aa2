"use client";

import { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import { motion, stagger, useAnimate } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  Target,
  GraduationCap,
  MessageCircle,
  X,
  Send,
  Loader2,
  AlertCircle,
  ArrowUp,
  Lightbulb,
  CreditCard as Cards,
  HelpCircle,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import ChatComponentParser from "@/components/ui/learning-hub/ChatComponentParser";
import ConceptExplorerComponent from "@/components/ui/learning-hub/ConceptExplorerComponent";
import StepByStepLearningComponent from "@/components/ui/learning-hub/StepByStepLearningComponent";
import InteractiveTimelineComponent from "@/components/ui/learning-hub/InteractiveTimelineComponent";
import ComparisonMatrixComponent from "@/components/ui/learning-hub/ComparisonMatrixComponent";
import FlashcardComponent from "@/components/ui/learning-hub/FlashcardComponent";
import InteractiveTrueFalseComponent from "@/components/ui/learning-hub/InteractiveTrueFalseComponent";
import InteractiveQuestionComponent from "@/components/ui/learning-hub/InteractiveQuestionComponent";
import InteractiveScenarioComponent from "@/components/ui/learning-hub/InteractiveScenarioComponent";

// Text Generate Effect Component
export const TextGenerateEffect = ({
  words,
  className,
  filter = true,
  duration = 0.5,
}: {
  words: string;
  className?: string;
  filter?: boolean;
  duration?: number;
}) => {
  const [scope, animate] = useAnimate();
  let wordsArray = words.split(" ");
  useEffect(() => {
    animate(
      "span",
      {
        opacity: 1,
        filter: filter ? "blur(0px)" : "none",
      },
      {
        duration: duration ? duration : 1,
        delay: stagger(0.2),
      }
    );
  }, [scope.current]);

  const renderWords = () => {
    return (
      <motion.div ref={scope}>
        {wordsArray.map((word, idx) => {
          return (
            <motion.span
              key={word + idx}
              className="text-charcoal opacity-0"
              style={{
                filter: filter ? "blur(10px)" : "none",
              }}
            >
              {word}{" "}
            </motion.span>
          );
        })}
      </motion.div>
    );
  };

  return (
    <div className={cn("font-bold", className)}>
      <div className="mt-4">
        <div className="text-charcoal text-2xl leading-snug tracking-wide">
          {renderWords()}
        </div>
      </div>
    </div>
  );
};

// Types for AI Chat
interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  mode?: 'assess' | 'tutor' | 'general';
  components?: MessageComponent[];
}

interface MessageComponent {
  type: 'flashcard' | 'question' | 'true_false' | 'practical' | 'mini_exam' | 'concept_explorer' | 'step_by_step' | 'timeline' | 'comparison_matrix';
  data: any;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  lastUpdated: Date;
}

export default function LearningHub() {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [certificate, setCertificate] = useState<CertificateRecord | null>(null);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (certificateId) {
      loadCertificateData();
    }
  }, [certificateId]);

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadCertificateData = async () => {
    try {
      setLoading(true);
      const cert = await getCertificate(certificateId);
      setCertificate(cert);

      // Load saved chat sessions from localStorage
      const savedSessions = localStorage.getItem(`learning-hub-${certificateId}`);
      if (savedSessions) {
        const sessions = JSON.parse(savedSessions);
        setChatSessions(sessions);
      }
    } catch (error) {
      console.error("Error loading certificate:", error);
    } finally {
      setLoading(false);
    }
  };

  const saveChatSessions = (sessions: ChatSession[]) => {
    localStorage.setItem(`learning-hub-${certificateId}`, JSON.stringify(sessions));
    setChatSessions(sessions);
  };

  const createNewSession = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: "New Learning Session",
      messages: [],
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    setCurrentSession(newSession);
    setShowWelcome(false);
  };

  const sendMessage = async (content: string, mode?: 'assess' | 'tutor') => {
    if (!content.trim() || !certificate || content.length > 1000) return;

    // Create user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date(),
      mode
    };

    // Update current session or create new one
    let session = currentSession;
    if (!session) {
      session = {
        id: Date.now().toString(),
        title: content.slice(0, 50) + (content.length > 50 ? '...' : ''),
        messages: [],
        createdAt: new Date(),
        lastUpdated: new Date()
      };
      setCurrentSession(session);
      setShowWelcome(false);
    }

    // Add user message
    session.messages.push(userMessage);
    setCurrentSession({ ...session });
    setMessage("");
    setIsTyping(true);

    try {
      // Call AI API
      const response = await fetch('/api/ai/learning-hub', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          mode,
          certificate,
          conversationHistory: session.messages.slice(-10) // Last 10 messages for context
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const aiResponse = await response.json();

      // Debug logging
      console.log('AI Response:', aiResponse);
      console.log('Components received:', aiResponse.components);

      // Create AI message
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        components: aiResponse.components
      };

      // Add AI message
      session.messages.push(aiMessage);
      session.lastUpdated = new Date();
      setCurrentSession({ ...session });

      // Save to localStorage
      const updatedSessions = chatSessions.filter(s => s.id !== session.id);
      updatedSessions.unshift(session);
      saveChatSessions(updatedSessions);

    } catch (error) {
      console.error("Error sending message:", error);

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: "I apologize, but I'm having trouble processing your request right now. Please try again.",
        timestamp: new Date()
      };

      session.messages.push(errorMessage);
      setCurrentSession({ ...session });
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Send message on Enter (without Shift) or Cmd+Enter
    if ((e.key === 'Enter' && !e.shiftKey) || (e.key === 'Enter' && (e.metaKey || e.ctrlKey))) {
      e.preventDefault();
      if (message.trim() && !isTyping) {
        sendMessage(message);
      }
    }
  };

  if (loading) {
    return (
      <div className="w-full h-[600px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-grey">Loading Learning Hub...</p>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="w-full h-[600px] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">Failed to load certificate data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-white">
      {showWelcome && !currentSession ? (
        // Welcome Screen - DTC Professional Design
        <div className="max-w-6xl mx-auto px-6 py-12">
          {/* Header Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-primary rounded-2xl mb-8">
              <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>

            <TextGenerateEffect
              words="AI Learning Companion"
              className="text-4xl font-bold mb-4"
            />

            <p className="text-xl text-grey max-w-2xl mx-auto">
              Your personalized AI tutor for <span className="font-semibold text-primary">{certificate.name}</span> certification
            </p>
          </div>

          {/* Main Service Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="group cursor-pointer"
              onClick={() => {
                createNewSession();
                sendMessage("I want you to assess my knowledge", 'assess');
              }}
            >
              <div className="bg-white border border-gray-200 rounded-2xl p-8 hover:border-accent hover:shadow-lg transition-all duration-300">
                <div className="flex items-start gap-6">
                  <div className="bg-accent/10 rounded-xl p-4 group-hover:bg-accent/20 transition-colors">
                    <Target className="h-8 w-8 text-accent" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-charcoal mb-3">Knowledge Assessment</h3>
                    <p className="text-grey text-lg leading-relaxed mb-6">
                      Evaluate your understanding with AI-generated questions tailored to your certification requirements. Get detailed feedback on strengths and areas for improvement.
                    </p>
                    <div className="inline-flex items-center text-accent font-semibold group-hover:gap-3 gap-2 transition-all">
                      <span>Begin Assessment</span>
                      <ArrowUp className="h-5 w-5 rotate-45 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="group cursor-pointer"
              onClick={() => {
                createNewSession();
                sendMessage("I want you to tutor me", 'tutor');
              }}
            >
              <div className="bg-white border border-gray-200 rounded-2xl p-8 hover:border-primary hover:shadow-lg transition-all duration-300">
                <div className="flex items-start gap-6">
                  <div className="bg-primary/10 rounded-xl p-4 group-hover:bg-primary/20 transition-colors">
                    <GraduationCap className="h-8 w-8 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-charcoal mb-3">Interactive Tutoring</h3>
                    <p className="text-grey text-lg leading-relaxed mb-6">
                      Learn through interactive lessons, flashcards, and practical scenarios. Receive step-by-step guidance adapted to your learning pace and style.
                    </p>
                    <div className="inline-flex items-center text-primary font-semibold group-hover:gap-3 gap-2 transition-all">
                      <span>Start Learning</span>
                      <ArrowUp className="h-5 w-5 rotate-45 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-gray-50 rounded-2xl p-8 mb-12"
          >
            <h4 className="text-xl font-bold text-charcoal text-center mb-8">Advanced Learning Features</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <Cards className="h-6 w-6 text-primary mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Interactive Flashcards</h5>
                <p className="text-sm text-grey">Dynamic learning cards</p>
              </div>
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <HelpCircle className="h-6 w-6 text-accent mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Mini Assessments</h5>
                <p className="text-sm text-grey">Quick knowledge checks</p>
              </div>
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <Lightbulb className="h-6 w-6 text-primary mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Practical Examples</h5>
                <p className="text-sm text-grey">Real-world scenarios</p>
              </div>
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <Zap className="h-6 w-6 text-accent mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Instant Feedback</h5>
                <p className="text-sm text-grey">Immediate responses</p>
              </div>
            </div>
          </motion.div>

          {/* Alternative Option */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
            className="text-center"
          >
            <p className="text-grey mb-4">Need something specific?</p>
            <Button
              onClick={createNewSession}
              variant="outline"
              size="lg"
              className="border-gray-300 text-charcoal hover:border-primary hover:text-primary"
            >
              <MessageCircle className="h-5 w-5 mr-2" />
              Start a Custom Conversation
            </Button>
          </motion.div>
        </div>
      ) : (
        // Chat Interface - DTC Professional Design
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="bg-white border border-gray-200 rounded-2xl shadow-sm overflow-hidden">
            {/* Professional Chat Header */}
            <div className="bg-white border-b border-gray-100 p-6 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {/* DTC Logo */}
                  <div className="bg-gradient-to-br from-primary to-primary-deep rounded-xl p-3 shadow-sm">
                    <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-charcoal font-display">
                      {currentSession?.title || "AI Learning Assistant"}
                    </h3>
                    <div className="flex items-center gap-2 text-grey">
                      <span className="text-sm font-medium">{certificate.name}</span>
                      <span className="w-1 h-1 bg-grey rounded-full"></span>
                      <span className="text-sm">Interactive Learning</span>
                      <div className="flex items-center gap-1 ml-2">
                        <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                        <span className="text-xs text-accent font-medium">Online</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-grey hover:text-charcoal hover:bg-gray-50 rounded-lg"
                    onClick={() => {
                      // Add functionality to clear chat
                      if (currentSession) {
                        setCurrentSession({
                          ...currentSession,
                          messages: []
                        });
                      }
                    }}
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-grey hover:text-charcoal hover:bg-gray-50 rounded-lg"
                    onClick={() => {
                      setCurrentSession(null);
                      setShowWelcome(true);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages Container */}
            <div className="h-[600px] overflow-y-auto bg-white">
              <div className="p-6 space-y-8">
                {currentSession?.messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={cn(
                      "flex gap-4",
                      msg.type === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    {/* AI Avatar */}
                    {msg.type === 'ai' && (
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                          </svg>
                        </div>
                      </div>
                    )}

                    <div
                      className={cn(
                        "max-w-[75%] rounded-2xl",
                        msg.type === 'user'
                          ? "bg-primary text-white px-6 py-4 shadow-sm"
                          : "bg-white border border-gray-100 shadow-sm"
                      )}
                    >
                      {msg.type === 'user' ? (
                        <p className="whitespace-pre-wrap leading-relaxed font-medium">{msg.content}</p>
                      ) : (
                        <div className="p-6">
                          {/* AI Message Content with Component Parser */}
                          <ChatComponentParser
                            content={msg.content}
                            onComplete={(componentType, correct) => {
                              console.log(`${componentType} completed:`, correct);
                              // You can add analytics or progress tracking here
                            }}
                          />

                          {/* Show component generation indicator */}
                          {(!msg.components || msg.components.length === 0) && (
                            <div className="mt-4 p-4 bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl">
                              <div className="flex items-center gap-3">
                                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                                <span className="text-primary font-medium text-sm">Generating interactive components...</span>
                              </div>
                            </div>
                          )}

                          {/* API-Generated Interactive Components */}
                          {msg.components && msg.components.length > 0 && (
                            <div className="mt-6 space-y-6">
                              {/* Component Success Indicator */}
                              <div className="flex items-center gap-2 text-sm text-emerald-600 mb-4">
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span className="font-medium">Interactive components loaded • {msg.components.length} component{msg.components.length > 1 ? 's' : ''}</span>
                              </div>
                              {msg.components.map((component, index) => (
                                <motion.div
                                  key={index}
                                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                                  animate={{ opacity: 1, y: 0, scale: 1 }}
                                  transition={{
                                    duration: 0.5,
                                    delay: index * 0.2,
                                    type: "spring",
                                    stiffness: 100
                                  }}
                                  className="bg-white border border-gray-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                                >
                                  {/* Component Type Badge */}
                                  <div className="absolute top-4 right-4 z-10">
                                    <div className="bg-primary text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                                      {component.type.replace('_', ' ').toUpperCase()}
                                    </div>
                                  </div>

                                  {component.type === 'concept_explorer' && (
                                    <ConceptExplorerComponent
                                      data={component.data}
                                      onComplete={() => console.log('Concept explorer completed')}
                                    />
                                  )}
                                  {component.type === 'step_by_step' && (
                                    <StepByStepLearningComponent
                                      data={component.data}
                                      onComplete={() => console.log('Step-by-step completed')}
                                    />
                                  )}
                                  {component.type === 'timeline' && (
                                    <InteractiveTimelineComponent
                                      data={component.data}
                                      onComplete={() => console.log('Timeline completed')}
                                    />
                                  )}
                                  {component.type === 'comparison_matrix' && (
                                    <ComparisonMatrixComponent
                                      data={component.data}
                                      onComplete={() => console.log('Comparison matrix completed')}
                                    />
                                  )}
                                  {component.type === 'flashcard' && (
                                    <FlashcardComponent
                                      data={component.data}
                                      onComplete={(correct) => console.log('Flashcard completed:', correct)}
                                    />
                                  )}
                                  {component.type === 'question' && (
                                    <InteractiveQuestionComponent
                                      data={component.data}
                                      onComplete={(correct) => console.log('Question completed:', correct)}
                                    />
                                  )}
                                  {component.type === 'true_false' && (
                                    <InteractiveTrueFalseComponent
                                      data={component.data}
                                      onComplete={(correct) => console.log('True/False completed:', correct)}
                                    />
                                  )}
                                  {component.type === 'practical' && (
                                    <InteractiveScenarioComponent
                                      data={component.data}
                                      onComplete={() => console.log('Practical completed')}
                                    />
                                  )}
                                  {component.type === 'mini_exam' && (
                                    <div className="bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl p-6">
                                      <h4 className="font-semibold text-primary mb-3 flex items-center gap-2">
                                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                        </svg>
                                        Mini Assessment Ready
                                      </h4>
                                      <div className="grid grid-cols-3 gap-4 text-sm text-charcoal mb-4">
                                        <div className="text-center">
                                          <div className="font-semibold text-lg text-primary">{component.data.questions?.length || 0}</div>
                                          <div className="text-grey">Questions</div>
                                        </div>
                                        <div className="text-center">
                                          <div className="font-semibold text-lg text-primary">{component.data.timeLimit || 'No limit'}</div>
                                          <div className="text-grey">Time Limit</div>
                                        </div>
                                        <div className="text-center">
                                          <div className="font-semibold text-lg text-primary">{component.data.passingScore || 70}%</div>
                                          <div className="text-grey">Passing Score</div>
                                        </div>
                                      </div>
                                      <Button className="w-full bg-primary hover:bg-primary-deep text-white font-medium">
                                        Start Assessment
                                      </Button>
                                    </div>
                                  )}
                                </motion.div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* User Avatar */}
                    {msg.type === 'user' && (
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-charcoal rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                          </svg>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {isTyping && (
                  <div className="flex gap-4 justify-start">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-100 shadow-sm rounded-2xl px-6 py-4 flex items-center gap-3">
                      <Loader2 className="h-5 w-5 animate-spin text-primary" />
                      <span className="text-grey font-medium">AI is thinking...</span>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Professional Input Section */}
            <div className="border-t border-gray-100 bg-white p-6">
              <div className="bg-gray-50 rounded-2xl border border-gray-200 p-4 focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/10 transition-all">
                <div className="flex gap-4 items-end">
                  <div className="flex-1">
                    <Textarea
                      value={message}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value.length <= 1000) {
                          setMessage(value);
                        }
                      }}
                      onKeyDown={handleKeyDown}
                      placeholder="Ask me anything about your learning journey..."
                      className="w-full min-h-[60px] max-h-[120px] resize-none border-0 bg-transparent text-charcoal placeholder:text-grey focus:ring-0 focus:outline-none text-base leading-relaxed"
                      disabled={isTyping}
                    />
                  </div>
                  <div className="flex gap-2">
                    {/* Quick Action Buttons */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => sendMessage("Assess my knowledge", "assess")}
                      disabled={isTyping}
                      className="text-grey hover:text-primary hover:bg-primary/5 text-sm font-medium"
                    >
                      Assess
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => sendMessage("Tutor me", "tutor")}
                      disabled={isTyping}
                      className="text-grey hover:text-primary hover:bg-primary/5 text-sm font-medium"
                    >
                      Tutor
                    </Button>
                    <Button
                      onClick={() => sendMessage(message)}
                      disabled={!message.trim() || isTyping}
                      size="lg"
                      className="bg-primary hover:bg-primary-deep text-white px-6 py-3 rounded-xl font-medium shadow-sm transition-all hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isTyping ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        <Send className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Input Helper Text */}
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                  <div className="flex items-center gap-4 text-sm text-grey">
                    <span className="flex items-center gap-1">
                      <kbd className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-mono">⌘</kbd>
                      <kbd className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-mono">Enter</kbd>
                      <span>to send</span>
                    </span>
                    <span className="flex items-center gap-1">
                      <kbd className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-mono">Shift</kbd>
                      <kbd className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-mono">Enter</kbd>
                      <span>for new line</span>
                    </span>
                  </div>
                  <div className={cn(
                    "text-sm font-medium",
                    message.length > 900 ? "text-red-500" :
                    message.length > 800 ? "text-amber-500" :
                    "text-grey"
                  )}>
                    {message.length}/1000
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
