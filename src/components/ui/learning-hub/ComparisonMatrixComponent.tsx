"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  <PERSON>it<PERSON>ompar<PERSON>, 
  CheckCircle, 
  X, 
  Info,
  Eye,
  EyeOff,
  RotateCcw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ComparisonItem {
  name: string;
  description: string;
  color: string;
}

interface ComparisonCriteria {
  name: string;
  description: string;
  values: { [key: string]: string };
}

interface ComparisonMatrixData {
  title: string;
  subtitle: string;
  items: ComparisonItem[];
  criteria: ComparisonCriteria[];
  insights: string[];
}

interface ComparisonMatrixComponentProps {
  data: ComparisonMatrixData;
  onComplete?: () => void;
}

export default function ComparisonMatrixComponent({ 
  data, 
  onComplete 
}: ComparisonMatrixComponentProps) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectedCriteria, setSelectedCriteria] = useState<string | null>(null);
  const [showInsights, setShowInsights] = useState(false);
  const [exploredCells, setExploredCells] = useState<Set<string>>(new Set());

  const toggleItemSelection = (itemName: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemName)) {
      newSelected.delete(itemName);
    } else {
      newSelected.add(itemName);
    }
    setSelectedItems(newSelected);
  };

  const handleCellExplore = (itemName: string, criteriaName: string) => {
    const cellKey = `${itemName}-${criteriaName}`;
    const newExplored = new Set(exploredCells);
    newExplored.add(cellKey);
    setExploredCells(newExplored);
    
    // Check if all cells have been explored
    const totalCells = data.items.length * data.criteria.length;
    if (newExplored.size === totalCells) {
      setShowInsights(true);
      onComplete?.();
    }
  };

  const resetMatrix = () => {
    setSelectedItems(new Set());
    setSelectedCriteria(null);
    setShowInsights(false);
    setExploredCells(new Set());
  };

  const getItemColor = (color: string) => {
    const colors = {
      blue: "bg-blue-500",
      green: "bg-green-500",
      purple: "bg-purple-500",
      amber: "bg-amber-500",
      red: "bg-red-500",
      indigo: "bg-indigo-500"
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getCellBgColor = (color: string, isSelected: boolean, isExplored: boolean) => {
    if (isSelected) {
      const colors = {
        blue: "bg-blue-50 border-blue-200",
        green: "bg-green-50 border-green-200",
        purple: "bg-purple-50 border-purple-200",
        amber: "bg-amber-50 border-amber-200",
        red: "bg-red-50 border-red-200",
        indigo: "bg-indigo-50 border-indigo-200"
      };
      return colors[color as keyof typeof colors] || colors.blue;
    }
    if (isExplored) {
      return "bg-gray-50 border-gray-200";
    }
    return "bg-white border-gray-100 hover:bg-gray-25";
  };

  return (
    <div className="w-full max-w-6xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-50 to-blue-50 p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-cyan-500 rounded-lg p-3">
              <GitCompare className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-charcoal">{data.title}</h3>
              <p className="text-grey">{data.subtitle}</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm text-grey">Explored</div>
              <div className="text-lg font-semibold text-cyan-600">
                {exploredCells.size}/{data.items.length * data.criteria.length}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetMatrix}
              className="text-cyan-600 hover:text-cyan-700"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Matrix */}
      <div className="p-6">
        {showInsights ? (
          // Insights View
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="space-y-6"
          >
            <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-8 text-center">
              <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-emerald-500" />
              </div>
              <h4 className="text-xl font-bold text-charcoal mb-2">Comparison Complete!</h4>
              <p className="text-grey mb-6">You've explored all aspects of the comparison</p>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h5 className="font-semibold text-charcoal mb-4 flex items-center gap-2">
                <Info className="h-5 w-5 text-cyan-500" />
                Key Insights
              </h5>
              <ul className="space-y-3">
                {data.insights.map((insight, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start gap-2 text-charcoal"
                  >
                    <span className="text-cyan-500 mt-1">•</span>
                    <span className="leading-relaxed">{insight}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        ) : (
          // Matrix View
          <div className="space-y-6">
            {/* Items Legend */}
            <div className="flex flex-wrap gap-3">
              {data.items.map((item, index) => (
                <button
                  key={item.name}
                  onClick={() => toggleItemSelection(item.name)}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 rounded-lg border-2 transition-all",
                    selectedItems.has(item.name)
                      ? "border-gray-400 bg-gray-50"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                >
                  <div className={cn("w-4 h-4 rounded", getItemColor(item.color))}></div>
                  <span className="font-medium text-charcoal">{item.name}</span>
                  {selectedItems.has(item.name) ? (
                    <Eye className="h-4 w-4 text-gray-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              ))}
            </div>

            {/* Comparison Table */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="text-left p-4 bg-gray-50 border border-gray-200 font-semibold text-charcoal">
                      Criteria
                    </th>
                    {data.items.map((item) => (
                      <th
                        key={item.name}
                        className={cn(
                          "text-center p-4 border border-gray-200 font-semibold",
                          selectedItems.has(item.name) || selectedItems.size === 0
                            ? "bg-gray-50 text-charcoal"
                            : "bg-gray-100 text-gray-400"
                        )}
                      >
                        <div className="flex items-center justify-center gap-2">
                          <div className={cn("w-3 h-3 rounded", getItemColor(item.color))}></div>
                          {item.name}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {data.criteria.map((criteria) => (
                    <tr key={criteria.name}>
                      <td className="p-4 bg-gray-50 border border-gray-200 font-medium text-charcoal">
                        <div>
                          <div className="font-semibold">{criteria.name}</div>
                          <div className="text-sm text-grey mt-1">{criteria.description}</div>
                        </div>
                      </td>
                      {data.items.map((item) => {
                        const cellKey = `${item.name}-${criteria.name}`;
                        const isExplored = exploredCells.has(cellKey);
                        const isHighlighted = selectedItems.has(item.name) || selectedItems.size === 0;
                        
                        return (
                          <td
                            key={`${item.name}-${criteria.name}`}
                            className={cn(
                              "p-4 border border-gray-200 transition-all cursor-pointer",
                              getCellBgColor(item.color, selectedItems.has(item.name), isExplored),
                              !isHighlighted && "opacity-50"
                            )}
                            onClick={() => handleCellExplore(item.name, criteria.name)}
                          >
                            <div className="text-sm text-charcoal leading-relaxed">
                              {criteria.values[item.name]}
                            </div>
                            {isExplored && (
                              <div className="mt-2 flex justify-end">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              </div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Instructions */}
            <div className="bg-cyan-50 border border-cyan-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
                <div>
                  <p className="text-cyan-800 font-medium mb-1">How to use this comparison:</p>
                  <ul className="text-cyan-700 text-sm space-y-1">
                    <li>• Click on items above to highlight specific columns</li>
                    <li>• Click on table cells to explore each comparison point</li>
                    <li>• Explore all cells to unlock key insights</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
