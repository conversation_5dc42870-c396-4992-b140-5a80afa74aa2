"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { CheckCircle, X, RotateCcw, Lightbulb, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface InteractiveQuestionData {
  question: string;
  options: string[];
  correct?: number;
  explanation?: string;
  hints?: string[];
}

interface InteractiveQuestionComponentProps {
  data: InteractiveQuestionData;
  onComplete?: (correct: boolean) => void;
}

export default function InteractiveQuestionComponent({ 
  data, 
  onComplete 
}: InteractiveQuestionComponentProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [answered, setAnswered] = useState(false);

  const handleAnswerSelect = (index: number) => {
    if (answered) return;
    setSelectedAnswer(index);
  };

  const handleSubmit = () => {
    if (selectedAnswer === null || answered) return;
    
    setShowResult(true);
    setAnswered(true);
    
    const isCorrect = data.correct !== undefined ? selectedAnswer === data.correct : true;
    onComplete?.(isCorrect);
  };

  const resetQuestion = () => {
    setSelectedAnswer(null);
    setShowResult(false);
    setAnswered(false);
    setShowHint(false);
  };

  const getOptionLetter = (index: number) => {
    return String.fromCharCode(65 + index); // A, B, C, D...
  };

  return (
    <div className="w-full max-w-3xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="bg-purple-500 rounded-lg p-2">
            <HelpCircle className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-charcoal">Multiple Choice Question</h3>
            <p className="text-sm text-grey">Choose the best answer</p>
          </div>
        </div>
      </div>

      {/* Question Content */}
      <div className="p-6">
        <div className="mb-6">
          <p className="text-lg text-charcoal leading-relaxed font-medium">
            {data.question}
          </p>
        </div>

        {/* Answer Options */}
        <div className="mb-6 space-y-3">
          {data.options.map((option, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "border-2 rounded-xl p-4 cursor-pointer transition-all",
                answered && "cursor-not-allowed",
                showResult && data.correct === index
                  ? "border-green-500 bg-green-50"
                  : showResult && selectedAnswer === index && data.correct !== index
                  ? "border-red-500 bg-red-50"
                  : showResult
                  ? "border-gray-200 bg-gray-50"
                  : selectedAnswer === index
                  ? "border-purple-500 bg-purple-50"
                  : "border-gray-200 hover:border-purple-300 hover:bg-purple-25"
              )}
              onClick={() => handleAnswerSelect(index)}
            >
              <div className="flex items-center gap-4">
                <div className={cn(
                  "w-10 h-10 rounded-full border-2 flex items-center justify-center font-bold",
                  showResult && data.correct === index && "bg-green-500 border-green-500 text-white",
                  showResult && selectedAnswer === index && data.correct !== index && "bg-red-500 border-red-500 text-white",
                  !showResult && selectedAnswer === index && "bg-purple-500 border-purple-500 text-white",
                  !showResult && selectedAnswer !== index && "border-gray-300 text-gray-600"
                )}>
                  {getOptionLetter(index)}
                </div>
                
                <span className={cn(
                  "flex-1 font-medium",
                  showResult && data.correct === index && "text-green-800",
                  showResult && selectedAnswer === index && data.correct !== index && "text-red-800",
                  showResult && selectedAnswer !== index && data.correct !== index && "text-grey",
                  !showResult && selectedAnswer === index && "text-purple-800",
                  !showResult && selectedAnswer !== index && "text-charcoal"
                )}>
                  {option}
                </span>
                
                {showResult && data.correct === index && (
                  <CheckCircle className="h-6 w-6 text-green-500" />
                )}
                {showResult && selectedAnswer === index && data.correct !== index && (
                  <X className="h-6 w-6 text-red-500" />
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 justify-center mb-6">
          {!answered && (
            <>
              <Button
                variant="outline"
                onClick={() => setShowHint(!showHint)}
                className="border-amber-300 text-amber-700 hover:bg-amber-50"
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                {showHint ? "Hide Hint" : "Show Hint"}
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={selectedAnswer === null}
                className="bg-purple-500 hover:bg-purple-600 text-white px-8"
              >
                Submit Answer
              </Button>
            </>
          )}
          
          {answered && (
            <Button
              onClick={resetQuestion}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/5"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
        </div>

        {/* Hint */}
        {showHint && !answered && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg"
          >
            <div className="flex items-start gap-2">
              <Lightbulb className="h-5 w-5 text-amber-600 mt-0.5" />
              <div>
                <p className="text-amber-800 font-medium mb-1">Hint:</p>
                <p className="text-amber-700 text-sm">
                  {data.hints?.[0] || "Think about the key principles and requirements we've discussed."}
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Result and Explanation */}
        {showResult && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={cn(
              "p-6 rounded-xl border-2",
              data.correct === undefined || selectedAnswer === data.correct
                ? "bg-green-50 border-green-200"
                : "bg-blue-50 border-blue-200"
            )}
          >
            <div className="flex items-start gap-3">
              <div className={cn(
                "rounded-full p-2",
                data.correct === undefined || selectedAnswer === data.correct
                  ? "bg-green-100"
                  : "bg-blue-100"
              )}>
                <CheckCircle className={cn(
                  "h-5 w-5",
                  data.correct === undefined || selectedAnswer === data.correct
                    ? "text-green-600"
                    : "text-blue-600"
                )} />
              </div>
              <div>
                <h4 className={cn(
                  "font-semibold mb-2",
                  data.correct === undefined || selectedAnswer === data.correct
                    ? "text-green-800"
                    : "text-blue-800"
                )}>
                  {data.correct === undefined || selectedAnswer === data.correct
                    ? "Correct! Well done!"
                    : "Not quite, but great effort!"}
                </h4>
                <p className={cn(
                  "leading-relaxed",
                  data.correct === undefined || selectedAnswer === data.correct
                    ? "text-green-700"
                    : "text-blue-700"
                )}>
                  {data.explanation || "Keep studying and you'll master these concepts!"}
                </p>
                
                {data.correct !== undefined && selectedAnswer !== data.correct && (
                  <div className="mt-3 p-3 bg-white rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-800">
                      <strong>The correct answer was:</strong> {getOptionLetter(data.correct)}) {data.options[data.correct]}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
