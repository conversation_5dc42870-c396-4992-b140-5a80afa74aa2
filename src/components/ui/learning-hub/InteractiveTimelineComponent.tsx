"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Calendar, 
  Clock, 
  ChevronRight, 
  ChevronLeft,
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  Info
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TimelineEvent {
  date: string;
  title: string;
  description: string;
  impact: string;
  keyPoints: string[];
  icon?: string;
}

interface InteractiveTimelineData {
  topic: string;
  subtitle: string;
  events: TimelineEvent[];
  conclusion: string;
}

interface InteractiveTimelineComponentProps {
  data: InteractiveTimelineData;
  onComplete?: () => void;
}

export default function InteractiveTimelineComponent({ 
  data, 
  onComplete 
}: InteractiveTimelineComponentProps) {
  const [currentEvent, setCurrentEvent] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(false);
  const [viewedEvents, setViewedEvents] = useState<Set<number>>(new Set([0]));
  const [showConclusion, setShowConclusion] = useState(false);

  const handleEventView = (index: number) => {
    setCurrentEvent(index);
    const newViewed = new Set(viewedEvents);
    newViewed.add(index);
    setViewedEvents(newViewed);
    
    if (newViewed.size === data.events.length && !showConclusion) {
      setShowConclusion(true);
      onComplete?.();
    }
  };

  const handleNext = () => {
    if (currentEvent < data.events.length - 1) {
      handleEventView(currentEvent + 1);
    }
  };

  const handlePrevious = () => {
    if (currentEvent > 0) {
      handleEventView(currentEvent - 1);
    }
  };

  const resetTimeline = () => {
    setCurrentEvent(0);
    setViewedEvents(new Set([0]));
    setShowConclusion(false);
    setIsAutoPlaying(false);
  };

  const currentEventData = data.events[currentEvent];

  return (
    <div className="w-full max-w-5xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-slate-600 rounded-lg p-3">
              <Calendar className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-charcoal">{data.topic}</h3>
              <p className="text-grey">{data.subtitle}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-grey">Progress</div>
            <div className="text-lg font-semibold text-slate-600">
              {viewedEvents.size}/{data.events.length}
            </div>
          </div>
        </div>
      </div>

      {/* Timeline Navigation */}
      <div className="bg-gray-50 p-4 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold text-charcoal">Timeline</h4>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
              className="text-slate-600 hover:text-slate-700"
            >
              {isAutoPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetTimeline}
              className="text-slate-600 hover:text-slate-700"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-300"></div>
          <div 
            className="absolute top-6 left-6 h-0.5 bg-slate-500 transition-all duration-500"
            style={{ width: `${(currentEvent / (data.events.length - 1)) * 100}%` }}
          ></div>
          
          {/* Timeline Points */}
          <div className="flex justify-between">
            {data.events.map((event, index) => (
              <button
                key={index}
                onClick={() => handleEventView(index)}
                className={cn(
                  "relative flex flex-col items-center p-2 rounded-lg transition-all",
                  "hover:bg-white hover:shadow-sm",
                  currentEvent === index && "bg-white shadow-sm"
                )}
              >
                <div className={cn(
                  "w-4 h-4 rounded-full border-2 transition-all",
                  viewedEvents.has(index) 
                    ? "bg-slate-500 border-slate-500"
                    : currentEvent === index
                    ? "bg-white border-slate-500"
                    : "bg-white border-gray-300"
                )}>
                  {viewedEvents.has(index) && (
                    <CheckCircle className="w-3 h-3 text-white" />
                  )}
                </div>
                <div className="mt-2 text-center">
                  <div className="text-xs font-medium text-charcoal">{event.date}</div>
                  <div className="text-xs text-grey max-w-20 truncate">{event.title}</div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {showConclusion ? (
          // Conclusion View
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-6"
          >
            <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-8">
              <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-emerald-500" />
              </div>
              <h4 className="text-xl font-bold text-charcoal mb-2">Timeline Complete!</h4>
              <p className="text-grey mb-6">You've explored the complete timeline of {data.topic}</p>
              
              <div className="bg-white rounded-lg p-6 text-left">
                <h5 className="font-semibold text-charcoal mb-3">Key Insights</h5>
                <p className="text-grey leading-relaxed">{data.conclusion}</p>
              </div>
            </div>
          </motion.div>
        ) : (
          // Event Content
          <motion.div
            key={currentEvent}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Event Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="bg-slate-500 rounded-lg p-3">
                  <Clock className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="text-xl font-semibold text-charcoal">{currentEventData.title}</h4>
                  <p className="text-slate-600 font-medium">{currentEventData.date}</p>
                </div>
              </div>
              <div className="text-sm text-grey">
                Event {currentEvent + 1} of {data.events.length}
              </div>
            </div>

            {/* Event Description */}
            <div className="bg-slate-50 rounded-xl p-6">
              <h5 className="font-semibold text-charcoal mb-3">What Happened</h5>
              <p className="text-charcoal leading-relaxed">{currentEventData.description}</p>
            </div>

            {/* Impact */}
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <h5 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                <Info className="h-5 w-5" />
                Impact & Significance
              </h5>
              <p className="text-blue-700 leading-relaxed">{currentEventData.impact}</p>
            </div>

            {/* Key Points */}
            <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-6">
              <h5 className="font-semibold text-emerald-800 mb-4">Key Points to Remember</h5>
              <ul className="space-y-2">
                {currentEventData.keyPoints.map((point, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start gap-2 text-emerald-700"
                  >
                    <span className="text-emerald-500 mt-1">•</span>
                    <span>{point}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        )}
      </div>

      {/* Navigation */}
      {!showConclusion && (
        <div className="bg-gray-50 p-6 flex justify-between items-center">
          <Button
            onClick={handlePrevious}
            disabled={currentEvent === 0}
            variant="outline"
            className="border-gray-300"
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous Event
          </Button>

          <div className="flex items-center gap-2">
            <span className="text-sm text-grey">
              {viewedEvents.has(currentEvent) ? "Viewed" : "New"}
            </span>
            {viewedEvents.has(currentEvent) && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>

          <Button
            onClick={handleNext}
            disabled={currentEvent === data.events.length - 1}
            className="bg-slate-500 hover:bg-slate-600 text-white"
          >
            Next Event
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
}
