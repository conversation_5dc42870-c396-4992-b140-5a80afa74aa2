"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, ChevronUp, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import FlashcardComponent from "./FlashcardComponent";
import InteractiveTrueFalseComponent from "./InteractiveTrueFalseComponent";
import InteractiveQuestionComponent from "./InteractiveQuestionComponent";
import InteractiveScenarioComponent from "./InteractiveScenarioComponent";
import ConceptExplorerComponent from "./ConceptExplorerComponent";
import StepByStepLearningComponent from "./StepByStepLearningComponent";
import InteractiveTimelineComponent from "./InteractiveTimelineComponent";
import ComparisonMatrixComponent from "./ComparisonMatrixComponent";

interface ParsedComponent {
  type: 'flashcard' | 'question' | 'true_false' | 'practical' | 'mini_exam' | 'concept_explorer' | 'step_by_step' | 'timeline' | 'comparison_matrix';
  data: any;
  originalText: string;
  startIndex: number;
  endIndex: number;
}

interface ChatComponentParserProps {
  content: string;
  onComplete?: (componentType: string, correct?: boolean) => void;
}

export default function ChatComponentParser({ content, onComplete }: ChatComponentParserProps) {
  const [parsedComponents, setParsedComponents] = useState<ParsedComponent[]>([]);
  const [cleanContent, setCleanContent] = useState<string>("");
  const [expandedComponents, setExpandedComponents] = useState<Set<number>>(new Set());

  useEffect(() => {
    parseContent(content);
  }, [content]);

  const parseContent = (text: string) => {
    const components: ParsedComponent[] = [];
    let cleanText = text;

    const sanitizeMarkdown = (input: string) => {
      let out = input;
      // Remove fenced code blocks
      out = out.replace(/```[\s\S]*?```/g, "");
      // Remove inline code
      out = out.replace(/`([^`]+)`/g, "$1");
      // Remove bold/italic markers
      out = out.replace(/\*\*([^*]+)\*\*/g, "$1");
      out = out.replace(/\*([^*]+)\*/g, "$1");
      out = out.replace(/_([^_]+)_/g, "$1");
      // Normalize list bullets
      out = out.replace(/^\s*[-*]\s+/gm, "• ");
      // Collapse excessive whitespace
      out = out.replace(/\n{3,}/g, "\n\n");
      // Trim stray braces-only JSON snippets inside lines
      out = out.replace(/\{\s*\}/g, "");
      return out.trim();
    };

    // Parse True/False questions (natural language format)
    const trueFalseNaturalRegex = new RegExp('\\*\\*True or False:\\*\\*\\s*(.*?)(?=\\n\\n|\\n\\*\\*|$)', 'g');
    let trueFalseMatch;

    while ((trueFalseMatch = trueFalseNaturalRegex.exec(text)) !== null) {
      const statement = trueFalseMatch[1].trim();

      components.push({
        type: 'true_false',
        data: {
          statement: statement,
          correct: null, // Will be determined by user interaction
          explanation: "Think about the GDPR requirements and when DPIAs are mandatory."
        },
        originalText: trueFalseMatch[0],
        startIndex: trueFalseMatch.index,
        endIndex: trueFalseMatch.index + trueFalseMatch[0].length
      });
    }

    // Parse multiple choice questions (natural language format)
    const questionNaturalRegex = new RegExp('(?:Here\'s a question|Question:|Let me ask you)[\\s\\S]*?(?=\\n\\n|\\n\\*\\*|$)', 'g');
    let questionMatch;
    while ((questionMatch = questionNaturalRegex.exec(text)) !== null) {
      const questionText = questionMatch[0];
      const optionsMatch = questionText.match(/[A-D]\)\s*([^\n]+)/g);

      if (optionsMatch && optionsMatch.length >= 2) {
        const questionContentMatch = questionText.match(new RegExp('(?:Here\'s a question|Question:|Let me ask you)[:\\s]*(.*?)(?=[A-D]\\)|$)', 's'));

        if (questionContentMatch) {
          components.push({
            type: 'question',
            data: {
              question: questionContentMatch[1].trim(),
              options: optionsMatch.map(opt => opt.replace(/^[A-D]\)\s*/, '')),
              correct: 0, // Default to first option
              explanation: "Consider the key principles and requirements."
            },
            originalText: questionText,
            startIndex: questionMatch.index,
            endIndex: questionMatch.index + questionMatch[0].length
          });
        }
      }
    }

    // Parse flashcards (structured format) - but convert complex ones to concept explorers
    const flashcardRegex = /\*\*\*[\s\S]*?\*\*Front of Flashcard:\*\*[\s\S]*?\*\*\*[\s\S]*?\*\*Back of Flashcard:\*\*[\s\S]*?\*\*\*/g;
    let flashcardMatch;

    while ((flashcardMatch = flashcardRegex.exec(text)) !== null) {
      const flashcardText = flashcardMatch[0];
      const frontMatch = flashcardText.match(/\*\*Front of Flashcard:\*\*([\s\S]*?)\*\*\*/);
      const backMatch = flashcardText.match(/\*\*Back of Flashcard:\*\*([\s\S]*?)\*\*\*/);

      if (frontMatch && backMatch) {
        const front = frontMatch[1].trim();
        const back = backMatch[1].trim();

        // If the back content is complex (long or contains multiple concepts), use concept explorer
        if (back.length > 300 || back.includes('\n\n') || back.includes('•') || back.includes('1.') || back.includes('Examples:')) {
          components.push({
            type: 'concept_explorer',
            data: {
              concept: front,
              definition: back.slice(0, 200),
              layers: [{
                title: "Core Understanding",
                description: back,
                examples: ["Practical application", "Real-world example"],
                realWorldApplication: "This concept is essential for GDPR compliance",
                icon: "Shield",
                color: "blue"
              }],
              keyTakeaways: ["Key insight from this concept"]
            },
            originalText: flashcardText,
            startIndex: flashcardMatch.index,
            endIndex: flashcardMatch.index + flashcardMatch[0].length
          });
        } else {
          // Simple definition - keep as flashcard
          components.push({
            type: 'flashcard',
            data: {
              front: front,
              back: back,
              category: "CIPP/E Concept"
            },
            originalText: flashcardText,
            startIndex: flashcardMatch.index,
            endIndex: flashcardMatch.index + flashcardMatch[0].length
          });
        }
      }
    }

    // Parse practical scenarios (natural language format)
    const scenarioRegex = new RegExp('(?:Here\'s a scenario|Scenario:|Consider this|Imagine you\'re)[\\s\\S]*?(?=\\n\\n|\\n\\*\\*|$)', 'g');
    let scenarioMatch;
    while ((scenarioMatch = scenarioRegex.exec(text)) !== null) {
      const scenarioText = scenarioMatch[0];

      components.push({
        type: 'practical',
        data: {
          scenario: scenarioText.trim(),
          explanation: "Apply the concepts you've learned to this real-world situation.",
          steps: ["Analyze the situation", "Identify key requirements", "Apply appropriate measures"]
        },
        originalText: scenarioText,
        startIndex: scenarioMatch.index,
        endIndex: scenarioMatch.index + scenarioMatch[0].length
      });
    }

    // Parse JSON-style flashcards (but only if they're explicitly mentioned)
    const jsonFlashcardRegex = /flashcard:\s*\{[\s\S]*?\}/g;
    let jsonFlashcardMatch;
    while ((jsonFlashcardMatch = jsonFlashcardRegex.exec(text)) !== null) {
      try {
        const jsonText = jsonFlashcardMatch[0].replace('flashcard:', '').trim();
        const data = parseJSONLike(jsonText);

        // Only create flashcard if it's a simple definition (not complex content)
        if (data.front && data.back && data.back.length < 200) {
          components.push({
            type: 'flashcard',
            data: {
              front: data.front,
              back: data.back,
              category: data.category || "Learning Concept"
            },
            originalText: jsonFlashcardMatch[0],
            startIndex: jsonFlashcardMatch.index,
            endIndex: jsonFlashcardMatch.index + jsonFlashcardMatch[0].length
          });
        } else if (data.front && data.back) {
          // Convert complex flashcard content to concept explorer
          components.push({
            type: 'concept_explorer',
            data: {
              concept: data.front,
              definition: data.back.slice(0, 150),
              layers: [{
                title: "Understanding",
                description: data.back,
                examples: ["Key application"],
                realWorldApplication: "This concept applies in practice",
                icon: "BookOpen",
                color: "blue"
              }],
              keyTakeaways: ["Main concept from this explanation"]
            },
            originalText: jsonFlashcardMatch[0],
            startIndex: jsonFlashcardMatch.index,
            endIndex: jsonFlashcardMatch.index + jsonFlashcardMatch[0].length
          });
        }
      } catch (error) {
        console.error("Error parsing flashcard JSON:", error);
      }
    }

    // Parse true/false questions
    const jsonTrueFalseRegex = /true_false:\s*\{[\s\S]*?\}/g;
    let jsonTrueFalseMatch;
    while ((jsonTrueFalseMatch = jsonTrueFalseRegex.exec(text)) !== null) {
      try {
        const jsonText = jsonTrueFalseMatch[0].replace('true_false:', '').trim();
        const data = parseJSONLike(jsonText);

        if (data.statement && typeof data.correct === 'boolean') {
          components.push({
            type: 'true_false',
            data: {
              statement: data.statement,
              correct: data.correct,
              explanation: data.explanation || "No explanation provided."
            },
            originalText: jsonTrueFalseMatch[0],
            startIndex: jsonTrueFalseMatch.index,
            endIndex: jsonTrueFalseMatch.index + jsonTrueFalseMatch[0].length
          });
        }
      } catch (error) {
        console.error("Error parsing true/false JSON:", error);
      }
    }

    // Parse questions
    const jsonQuestionRegex = /question:\s*\{[\s\S]*?\}/g;
    let jsonQuestionMatch;
    while ((jsonQuestionMatch = jsonQuestionRegex.exec(text)) !== null) {
      try {
        const jsonText = jsonQuestionMatch[0].replace('question:', '').trim();
        const data = parseJSONLike(jsonText);

        if (data.question && data.options && Array.isArray(data.options)) {
          components.push({
            type: 'question',
            data: {
              question: data.question,
              options: data.options,
              correct: data.correct || 0,
              explanation: data.explanation || "No explanation provided."
            },
            originalText: jsonQuestionMatch[0],
            startIndex: jsonQuestionMatch.index,
            endIndex: jsonQuestionMatch.index + jsonQuestionMatch[0].length
          });
        }
      } catch (error) {
        console.error("Error parsing question JSON:", error);
      }
    }

    // Sort components by their position in the text
    components.sort((a, b) => a.startIndex - b.startIndex);

    // Remove component text from content and clean up
    let offset = 0;
    components.forEach(component => {
      const adjustedStart = component.startIndex - offset;
      const adjustedEnd = component.endIndex - offset;
      cleanText = cleanText.slice(0, adjustedStart) + cleanText.slice(adjustedEnd);
      offset += component.originalText.length;
    });

    // Clean up extra asterisks and formatting
    cleanText = cleanText
      .replace(/\*\*\*/g, '')
      .replace(/\n\s*\n\s*\n/g, '\n\n');

    cleanText = sanitizeMarkdown(cleanText);

    setParsedComponents(components);
    setCleanContent(cleanText);
  };

  const parseJSONLike = (text: string): any => {
    try {
      // Try to parse as valid JSON first
      return JSON.parse(text);
    } catch (error) {
      // Fallback: simple key-value extraction
      const result: any = {};

      // Extract simple key: value pairs
      const keyValuePattern = /(\w+):\s*"([^"]+)"/g;
      let match;
      while ((match = keyValuePattern.exec(text)) !== null) {
        result[match[1]] = match[2];
      }

      // Extract boolean values
      const boolPattern = /(\w+):\s*(true|false)/g;
      while ((match = boolPattern.exec(text)) !== null) {
        result[match[1]] = match[2] === 'true';
      }

      // Extract arrays (simple format)
      const arrayPattern = /(\w+):\s*\[([^\]]+)\]/g;
      while ((match = arrayPattern.exec(text)) !== null) {
        result[match[1]] = match[2].split(',').map(item => item.trim().replace(/"/g, ''));
      }

      return result;
    }
  };

  const toggleComponent = (index: number) => {
    const newExpanded = new Set(expandedComponents);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedComponents(newExpanded);
  };

  const renderComponent = (component: ParsedComponent, index: number) => {
    const isExpanded = expandedComponents.has(index);

    return (
      <motion.div
        key={index}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className="my-6"
      >
        <Button
          variant="ghost"
          onClick={() => toggleComponent(index)}
          className="w-full p-4 h-auto bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl hover:from-primary/10 hover:to-accent/10 transition-all"
        >
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="bg-primary rounded-lg p-2">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
              <div className="text-left">
                <div className="font-semibold text-charcoal capitalize">
                  {component.type.replace('_', ' ')} Component
                </div>
                <div className="text-sm text-grey">
                  Click to {isExpanded ? 'collapse' : 'expand'} interactive element
                </div>
              </div>
            </div>
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 text-grey" />
            ) : (
              <ChevronDown className="h-5 w-5 text-grey" />
            )}
          </div>
        </Button>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="mt-4 p-6 bg-white border border-gray-100 rounded-xl shadow-sm">
                {component.type === 'flashcard' && (
                  <FlashcardComponent
                    data={component.data}
                    onComplete={(correct) => onComplete?.('flashcard', correct)}
                  />
                )}
                {component.type === 'question' && (
                  <InteractiveQuestionComponent
                    data={component.data}
                    onComplete={(correct) => onComplete?.('question', correct)}
                  />
                )}
                {component.type === 'true_false' && (
                  <InteractiveTrueFalseComponent
                    data={component.data}
                    onComplete={(correct) => onComplete?.('true_false', correct)}
                  />
                )}
                {component.type === 'practical' && (
                  <InteractiveScenarioComponent
                    data={component.data}
                    onComplete={() => onComplete?.('practical')}
                  />
                )}
                {component.type === 'concept_explorer' && (
                  <ConceptExplorerComponent
                    data={component.data}
                    onComplete={() => onComplete?.('concept_explorer')}
                  />
                )}
                {component.type === 'step_by_step' && (
                  <StepByStepLearningComponent
                    data={component.data}
                    onComplete={() => onComplete?.('step_by_step')}
                  />
                )}
                {component.type === 'timeline' && (
                  <InteractiveTimelineComponent
                    data={component.data}
                    onComplete={() => onComplete?.('timeline')}
                  />
                )}
                {component.type === 'comparison_matrix' && (
                  <ComparisonMatrixComponent
                    data={component.data}
                    onComplete={() => onComplete?.('comparison_matrix')}
                  />
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Render cleaned content */}
      <div className="prose prose-sm max-w-none text-charcoal">
        <div className="whitespace-pre-wrap leading-relaxed text-base">
          {cleanContent}
        </div>
      </div>

      {/* Render interactive components */}
      {parsedComponents.length > 0 && (
        <div className="space-y-4">
          {parsedComponents.map((component, index) => renderComponent(component, index))}
        </div>
      )}
    </div>
  );
}
