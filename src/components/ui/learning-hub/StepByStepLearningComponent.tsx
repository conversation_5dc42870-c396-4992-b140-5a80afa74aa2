"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  <PERSON>, 
  Pause, 
  RotateCcw, 
  CheckCircle, 
  ArrowRight,
  ArrowLeft,
  Lightbulb,
  BookOpen,
  Target
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface LearningStep {
  title: string;
  content: string;
  tip?: string;
  visual?: string;
  checkpoint: string;
}

interface StepByStepLearningData {
  topic: string;
  objective: string;
  steps: LearningStep[];
  summary: string;
}

interface StepByStepLearningComponentProps {
  data: StepByStepLearningData;
  onComplete?: () => void;
}

export default function StepByStepLearningComponent({ 
  data, 
  onComplete 
}: StepByStepLearningComponentProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [showTip, setShowTip] = useState(false);

  const handleStepComplete = () => {
    const newCompleted = new Set(completedSteps);
    newCompleted.add(currentStep);
    setCompletedSteps(newCompleted);

    if (currentStep < data.steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete?.();
    }
  };

  const handleNext = () => {
    if (currentStep < data.steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const resetLearning = () => {
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setIsPlaying(false);
    setShowTip(false);
  };

  const currentStepData = data.steps[currentStep];
  const isCurrentStepCompleted = completedSteps.has(currentStep);
  const allStepsCompleted = completedSteps.size === data.steps.length;

  return (
    <div className="w-full max-w-4xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-indigo-500 rounded-lg p-3">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-charcoal">Step-by-Step Learning</h3>
              <p className="text-grey">{data.topic}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-grey">Progress</div>
            <div className="text-lg font-semibold text-indigo-600">
              {completedSteps.size}/{data.steps.length}
            </div>
          </div>
        </div>
        
        {/* Objective */}
        <div className="mt-4 bg-white rounded-lg p-4 border border-indigo-200">
          <div className="flex items-center gap-2 mb-2">
            <Target className="h-4 w-4 text-indigo-600" />
            <span className="font-medium text-indigo-800">Learning Objective</span>
          </div>
          <p className="text-indigo-700 text-sm">{data.objective}</p>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-gray-100 h-3">
        <div 
          className="bg-gradient-to-r from-indigo-500 to-purple-500 h-full transition-all duration-500"
          style={{ width: `${(completedSteps.size / data.steps.length) * 100}%` }}
        />
      </div>

      {/* Content */}
      <div className="p-6">
        {allStepsCompleted ? (
          // Summary View
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-6"
          >
            <div className="bg-green-50 border border-green-200 rounded-xl p-8">
              <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <h4 className="text-xl font-bold text-charcoal mb-2">Learning Complete!</h4>
              <p className="text-grey mb-6">You've successfully completed all steps for {data.topic}</p>
              
              <div className="bg-white rounded-lg p-6 text-left">
                <h5 className="font-semibold text-charcoal mb-3">Summary</h5>
                <p className="text-grey leading-relaxed">{data.summary}</p>
              </div>
            </div>
            
            <Button
              onClick={resetLearning}
              variant="outline"
              className="border-indigo-500 text-indigo-600 hover:bg-indigo-50"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Review Again
            </Button>
          </motion.div>
        ) : (
          // Step Content
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Step Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-10 h-10 rounded-full border-2 flex items-center justify-center font-bold",
                  isCurrentStepCompleted 
                    ? "bg-green-500 border-green-500 text-white"
                    : "bg-indigo-500 border-indigo-500 text-white"
                )}>
                  {isCurrentStepCompleted ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    currentStep + 1
                  )}
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-charcoal">{currentStepData.title}</h4>
                  <p className="text-sm text-grey">Step {currentStep + 1} of {data.steps.length}</p>
                </div>
              </div>
              
              {currentStepData.tip && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowTip(!showTip)}
                  className="text-amber-600 hover:text-amber-700 hover:bg-amber-50"
                >
                  <Lightbulb className="h-4 w-4 mr-1" />
                  {showTip ? "Hide Tip" : "Show Tip"}
                </Button>
              )}
            </div>

            {/* Tip */}
            {showTip && currentStepData.tip && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-amber-50 border border-amber-200 rounded-lg p-4"
              >
                <div className="flex items-start gap-2">
                  <Lightbulb className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <p className="text-amber-800 font-medium mb-1">Pro Tip:</p>
                    <p className="text-amber-700 text-sm">{currentStepData.tip}</p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Step Content */}
            <div className="bg-gray-50 rounded-xl p-6">
              <div className="prose prose-sm max-w-none">
                <p className="text-charcoal leading-relaxed whitespace-pre-line">
                  {currentStepData.content}
                </p>
              </div>
              
              {currentStepData.visual && (
                <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-blue-800 font-medium text-sm">Visual Guide</span>
                  </div>
                  <p className="text-blue-700 text-sm">{currentStepData.visual}</p>
                </div>
              )}
            </div>

            {/* Checkpoint */}
            <div className="bg-indigo-50 border border-indigo-200 rounded-xl p-6">
              <h5 className="font-semibold text-indigo-800 mb-3 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Checkpoint
              </h5>
              <p className="text-indigo-700 mb-4">{currentStepData.checkpoint}</p>
              
              {!isCurrentStepCompleted && (
                <Button
                  onClick={handleStepComplete}
                  className="bg-indigo-500 hover:bg-indigo-600 text-white"
                >
                  Mark as Understood
                  <CheckCircle className="h-4 w-4 ml-2" />
                </Button>
              )}
              
              {isCurrentStepCompleted && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">Step completed!</span>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>

      {/* Navigation */}
      {!allStepsCompleted && (
        <div className="bg-gray-50 p-6 flex justify-between items-center">
          <Button
            onClick={handlePrevious}
            disabled={currentStep === 0}
            variant="outline"
            className="border-gray-300"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex gap-2">
            {data.steps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-colors",
                  completedSteps.has(index) ? "bg-green-500" :
                  index === currentStep ? "bg-indigo-500" : "bg-gray-300"
                )}
              />
            ))}
          </div>

          <Button
            onClick={handleNext}
            disabled={currentStep === data.steps.length - 1}
            className="bg-indigo-500 hover:bg-indigo-600 text-white"
          >
            Next Step
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
}
