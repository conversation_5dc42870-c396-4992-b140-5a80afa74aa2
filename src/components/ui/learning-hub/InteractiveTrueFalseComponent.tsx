"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>Circle, X, RotateCcw, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface InteractiveTrueFalseData {
  statement: string;
  correct?: boolean | null;
  explanation?: string;
  hints?: string[];
}

interface InteractiveTrueFalseComponentProps {
  data: InteractiveTrueFalseData;
  onComplete?: (correct: boolean) => void;
}

export default function InteractiveTrueFalseComponent({ 
  data, 
  onComplete 
}: InteractiveTrueFalseComponentProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [answered, setAnswered] = useState(false);

  const handleAnswerSelect = (answer: boolean) => {
    if (answered) return;
    setSelectedAnswer(answer);
  };

  const handleSubmit = () => {
    if (selectedAnswer === null || answered) return;
    
    setShowResult(true);
    setAnswered(true);
    
    // For interactive questions where correct answer isn't predetermined,
    // we'll provide feedback based on common GDPR knowledge
    const isCorrect = data.correct !== null ? selectedAnswer === data.correct : true;
    onComplete?.(isCorrect);
  };

  const resetQuestion = () => {
    setSelectedAnswer(null);
    setShowResult(false);
    setAnswered(false);
    setShowHint(false);
  };

  const getExplanation = () => {
    if (data.explanation) return data.explanation;
    
    // Default explanations for DPIA questions
    if (data.statement.toLowerCase().includes('dpia')) {
      if (selectedAnswer === false) {
        return "Correct! DPIAs are required for ANY processing likely to result in high risk, not just special categories of data. This includes systematic monitoring, large-scale processing, automated decision-making, and more.";
      } else {
        return "Not quite. While special categories of data often require DPIAs, they're not the only trigger. DPIAs are required for ANY processing likely to result in high risk to individuals' rights and freedoms.";
      }
    }
    
    return "Great thinking! Consider how this applies to real-world scenarios.";
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="bg-blue-500 rounded-lg p-2">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-charcoal">True or False Question</h3>
            <p className="text-sm text-grey">Test your understanding</p>
          </div>
        </div>
      </div>

      {/* Question Content */}
      <div className="p-6">
        <div className="mb-6">
          <p className="text-lg text-charcoal leading-relaxed font-medium">
            {data.statement}
          </p>
        </div>

        {/* Answer Options */}
        <div className="mb-6">
          <div className="grid grid-cols-2 gap-4">
            {/* True Option */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className={cn(
                "border-2 rounded-xl p-6 cursor-pointer transition-all text-center",
                answered && "cursor-not-allowed",
                showResult && data.correct === true
                  ? "border-green-500 bg-green-50 text-green-800"
                  : showResult && selectedAnswer === true && data.correct === false
                  ? "border-red-500 bg-red-50 text-red-800"
                  : showResult
                  ? "border-gray-200 bg-gray-50 text-grey"
                  : selectedAnswer === true
                  ? "border-blue-500 bg-blue-50 text-blue-800"
                  : "border-gray-200 hover:border-blue-300 hover:bg-blue-25"
              )}
              onClick={() => handleAnswerSelect(true)}
            >
              <div className="flex items-center justify-center gap-3">
                <div className={cn(
                  "w-10 h-10 rounded-full border-2 flex items-center justify-center font-bold text-lg",
                  showResult && data.correct === true && "bg-green-500 border-green-500 text-white",
                  showResult && selectedAnswer === true && data.correct === false && "bg-red-500 border-red-500 text-white",
                  !showResult && selectedAnswer === true && "bg-blue-500 border-blue-500 text-white",
                  !showResult && selectedAnswer !== true && "border-gray-300"
                )}>
                  T
                </div>
                <span className="font-semibold text-xl">True</span>
                
                {showResult && data.correct === true && (
                  <CheckCircle className="h-6 w-6 text-green-500" />
                )}
                {showResult && selectedAnswer === true && data.correct === false && (
                  <X className="h-6 w-6 text-red-500" />
                )}
              </div>
            </motion.div>

            {/* False Option */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className={cn(
                "border-2 rounded-xl p-6 cursor-pointer transition-all text-center",
                answered && "cursor-not-allowed",
                showResult && data.correct === false
                  ? "border-green-500 bg-green-50 text-green-800"
                  : showResult && selectedAnswer === false && data.correct === true
                  ? "border-red-500 bg-red-50 text-red-800"
                  : showResult
                  ? "border-gray-200 bg-gray-50 text-grey"
                  : selectedAnswer === false
                  ? "border-blue-500 bg-blue-50 text-blue-800"
                  : "border-gray-200 hover:border-blue-300 hover:bg-blue-25"
              )}
              onClick={() => handleAnswerSelect(false)}
            >
              <div className="flex items-center justify-center gap-3">
                <div className={cn(
                  "w-10 h-10 rounded-full border-2 flex items-center justify-center font-bold text-lg",
                  showResult && data.correct === false && "bg-green-500 border-green-500 text-white",
                  showResult && selectedAnswer === false && data.correct === true && "bg-red-500 border-red-500 text-white",
                  !showResult && selectedAnswer === false && "bg-blue-500 border-blue-500 text-white",
                  !showResult && selectedAnswer !== false && "border-gray-300"
                )}>
                  F
                </div>
                <span className="font-semibold text-xl">False</span>
                
                {showResult && data.correct === false && (
                  <CheckCircle className="h-6 w-6 text-green-500" />
                )}
                {showResult && selectedAnswer === false && data.correct === true && (
                  <X className="h-6 w-6 text-red-500" />
                )}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 justify-center mb-6">
          {!answered && (
            <>
              <Button
                variant="outline"
                onClick={() => setShowHint(!showHint)}
                className="border-amber-300 text-amber-700 hover:bg-amber-50"
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                {showHint ? "Hide Hint" : "Show Hint"}
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={selectedAnswer === null}
                className="bg-blue-500 hover:bg-blue-600 text-white px-8"
              >
                Submit Answer
              </Button>
            </>
          )}
          
          {answered && (
            <Button
              onClick={resetQuestion}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/5"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
        </div>

        {/* Hint */}
        {showHint && !answered && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg"
          >
            <div className="flex items-start gap-2">
              <Lightbulb className="h-5 w-5 text-amber-600 mt-0.5" />
              <div>
                <p className="text-amber-800 font-medium mb-1">Hint:</p>
                <p className="text-amber-700 text-sm">
                  Think about Article 35 of GDPR and the three specific examples it provides. 
                  Are special categories the only trigger for DPIAs?
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Result and Explanation */}
        {showResult && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={cn(
              "p-6 rounded-xl border-2",
              data.correct === null || selectedAnswer === data.correct
                ? "bg-green-50 border-green-200"
                : "bg-blue-50 border-blue-200"
            )}
          >
            <div className="flex items-start gap-3">
              <div className={cn(
                "rounded-full p-2",
                data.correct === null || selectedAnswer === data.correct
                  ? "bg-green-100"
                  : "bg-blue-100"
              )}>
                <CheckCircle className={cn(
                  "h-5 w-5",
                  data.correct === null || selectedAnswer === data.correct
                    ? "text-green-600"
                    : "text-blue-600"
                )} />
              </div>
              <div>
                <h4 className={cn(
                  "font-semibold mb-2",
                  data.correct === null || selectedAnswer === data.correct
                    ? "text-green-800"
                    : "text-blue-800"
                )}>
                  {data.correct === null || selectedAnswer === data.correct
                    ? "Excellent reasoning!"
                    : "Good attempt! Let's learn more:"}
                </h4>
                <p className={cn(
                  "leading-relaxed",
                  data.correct === null || selectedAnswer === data.correct
                    ? "text-green-700"
                    : "text-blue-700"
                )}>
                  {getExplanation()}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
