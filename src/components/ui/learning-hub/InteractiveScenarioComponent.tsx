"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Briefcase, 
  CheckCircle, 
  ArrowRight, 
  Lightbulb, 
  Users, 
  Shield,
  FileText,
  RotateCcw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";

interface InteractiveScenarioData {
  scenario: string;
  explanation?: string;
  steps?: string[];
  keyPoints?: string[];
  questions?: string[];
}

interface InteractiveScenarioComponentProps {
  data: InteractiveScenarioData;
  onComplete?: () => void;
}

export default function InteractiveScenarioComponent({ 
  data, 
  onComplete 
}: InteractiveScenarioComponentProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [userResponses, setUserResponses] = useState<string[]>([]);
  const [currentResponse, setCurrentResponse] = useState("");
  const [completed, setCompleted] = useState(false);
  const [showGuidance, setShowGuidance] = useState(false);

  const steps = [
    { 
      title: "Analyze the Scenario", 
      icon: Briefcase,
      description: "Read and understand the situation",
      color: "blue"
    },
    { 
      title: "Identify Key Issues", 
      icon: Shield,
      description: "What are the main privacy concerns?",
      color: "amber"
    },
    { 
      title: "Apply GDPR Principles", 
      icon: FileText,
      description: "Which regulations and principles apply?",
      color: "green"
    },
    { 
      title: "Recommend Actions", 
      icon: Users,
      description: "What steps should be taken?",
      color: "purple"
    }
  ];

  const handleNext = () => {
    if (currentStep === 0) {
      // Just reading the scenario
      setCurrentStep(1);
    } else if (currentStep < steps.length) {
      // Save user response and move to next step
      const newResponses = [...userResponses];
      newResponses[currentStep - 1] = currentResponse;
      setUserResponses(newResponses);
      setCurrentResponse("");
      
      if (currentStep === steps.length - 1) {
        setCompleted(true);
        onComplete?.();
      } else {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      if (currentStep > 1) {
        setCurrentResponse(userResponses[currentStep - 2] || "");
      }
    }
  };

  const resetScenario = () => {
    setCurrentStep(0);
    setUserResponses([]);
    setCurrentResponse("");
    setCompleted(false);
    setShowGuidance(false);
  };

  const getStepColor = (step: number) => {
    const colors = {
      blue: "from-blue-500 to-blue-600",
      amber: "from-amber-500 to-amber-600", 
      green: "from-green-500 to-green-600",
      purple: "from-purple-500 to-purple-600"
    };
    return colors[steps[step]?.color as keyof typeof colors] || colors.blue;
  };

  if (completed) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-4xl mx-auto bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200 p-8 shadow-sm"
      >
        <div className="text-center">
          <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
          <h3 className="text-2xl font-bold text-charcoal mb-2">Scenario Analysis Complete!</h3>
          <p className="text-grey mb-6">You've worked through this practical scenario step by step.</p>
          
          <div className="bg-white rounded-xl p-6 mb-6 text-left">
            <h4 className="font-semibold text-charcoal mb-4">Your Analysis Summary:</h4>
            <div className="space-y-4">
              {userResponses.map((response, index) => (
                <div key={index} className="border-l-4 border-primary pl-4">
                  <h5 className="font-medium text-charcoal">{steps[index + 1]?.title}</h5>
                  <p className="text-grey text-sm mt-1">{response}</p>
                </div>
              ))}
            </div>
          </div>
          
          <Button
            onClick={resetScenario}
            variant="outline"
            className="border-primary text-primary hover:bg-primary/5"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Analyze Again
          </Button>
        </div>
      </motion.div>
    );
  }

  const currentStepData = steps[currentStep];
  const IconComponent = currentStepData?.icon || Briefcase;

  return (
    <div className="w-full max-w-4xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Progress Bar */}
      <div className="bg-gray-100 h-3">
        <div 
          className="bg-gradient-to-r from-primary to-accent h-full transition-all duration-500 ease-out"
          style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
        />
      </div>

      {/* Header */}
      <div className={cn(
        "bg-gradient-to-r p-6 border-b border-gray-100",
        currentStep === 0 ? "from-slate-50 to-gray-50" : `bg-gradient-to-r ${getStepColor(currentStep)}`
      )}>
        <div className="flex items-center gap-4">
          <div className={cn(
            "rounded-lg p-3",
            currentStep === 0 ? "bg-white" : "bg-white/20"
          )}>
            <IconComponent className={cn(
              "h-6 w-6",
              currentStep === 0 ? "text-charcoal" : "text-white"
            )} />
          </div>
          <div>
            <h3 className={cn(
              "text-xl font-semibold",
              currentStep === 0 ? "text-charcoal" : "text-white"
            )}>
              {currentStep === 0 ? "Practical Scenario" : currentStepData.title}
            </h3>
            <p className={cn(
              "text-sm",
              currentStep === 0 ? "text-grey" : "text-white/80"
            )}>
              {currentStep === 0 ? "Real-world application" : currentStepData.description}
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {currentStep === 0 ? (
          // Scenario Display
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="bg-slate-50 border border-slate-200 rounded-xl p-6">
              <h4 className="font-semibold text-charcoal mb-3 flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-slate-600" />
                Scenario
              </h4>
              <p className="text-charcoal leading-relaxed">{data.scenario}</p>
            </div>
            
            <div className="text-center">
              <p className="text-grey mb-4">Ready to analyze this scenario step by step?</p>
              <Button
                onClick={handleNext}
                className="bg-primary hover:bg-primary-deep text-white px-8"
              >
                Start Analysis
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        ) : (
          // Interactive Steps
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="font-semibold text-charcoal mb-3">
                Step {currentStep}: {currentStepData.title}
              </h4>
              <p className="text-grey mb-4">{currentStepData.description}</p>
              
              <Textarea
                value={currentResponse}
                onChange={(e) => setCurrentResponse(e.target.value)}
                placeholder="Share your thoughts and analysis here..."
                className="min-h-[120px] resize-none border-gray-300 focus:border-primary focus:ring-primary"
              />
            </div>

            {/* Guidance */}
            <div className="flex justify-between items-center">
              <Button
                variant="ghost"
                onClick={() => setShowGuidance(!showGuidance)}
                className="text-amber-600 hover:text-amber-700 hover:bg-amber-50"
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                {showGuidance ? "Hide" : "Show"} Guidance
              </Button>
              
              <div className="flex gap-2">
                {steps.slice(1).map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors",
                      index < currentStep ? "bg-primary" : 
                      index === currentStep - 1 ? "bg-primary/60" : "bg-gray-300"
                    )}
                  />
                ))}
              </div>
            </div>

            {showGuidance && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-amber-50 border border-amber-200 rounded-lg p-4"
              >
                <div className="flex items-start gap-2">
                  <Lightbulb className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <p className="text-amber-800 font-medium mb-1">Guidance:</p>
                    <p className="text-amber-700 text-sm">
                      {currentStep === 1 && "Look for data processing activities, personal data types, and potential risks to individuals."}
                      {currentStep === 2 && "Consider lawful basis, data minimization, purpose limitation, and other GDPR principles."}
                      {currentStep === 3 && "Think about immediate actions, compliance measures, and long-term improvements."}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        )}
      </div>

      {/* Navigation */}
      {currentStep > 0 && (
        <div className="bg-gray-50 p-6 flex justify-between items-center">
          <Button
            onClick={handlePrevious}
            variant="outline"
            className="border-gray-300"
          >
            Previous
          </Button>

          <Button
            onClick={handleNext}
            disabled={currentStep > 0 && !currentResponse.trim()}
            className="bg-primary hover:bg-primary-deep text-white"
          >
            {currentStep === steps.length - 1 ? "Complete Analysis" : "Next Step"}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
}
