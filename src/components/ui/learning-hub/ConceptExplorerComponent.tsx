"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Lightbulb, 
  ArrowRight, 
  BookOpen, 
  Users, 
  Shield,
  Eye,
  CheckCircle,
  ChevronDown,
  ChevronRight
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ConceptLayer {
  title: string;
  description: string;
  examples: string[];
  realWorldApplication: string;
  icon: string; // Icon name as string
  color: string;
}

interface ConceptExplorerData {
  concept: string;
  definition: string;
  layers: ConceptLayer[];
  keyTakeaways: string[];
}

interface ConceptExplorerComponentProps {
  data: ConceptExplorerData;
  onComplete?: () => void;
}

export default function ConceptExplorerComponent({
  data,
  onComplete
}: ConceptExplorerComponentProps) {
  const [currentLayer, setCurrentLayer] = useState(0);
  const [expandedExamples, setExpandedExamples] = useState<Set<number>>(new Set());
  const [completed, setCompleted] = useState(false);

  // Function to get icon component from string name
  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      Shield,
      Users,
      BookOpen,
      Eye,
      CheckCircle,
      Lightbulb
    };
    return iconMap[iconName] || BookOpen; // Default to BookOpen if icon not found
  };

  const handleLayerComplete = () => {
    if (currentLayer < data.layers.length - 1) {
      setCurrentLayer(currentLayer + 1);
    } else {
      setCompleted(true);
      onComplete?.();
    }
  };

  const toggleExample = (index: number) => {
    const newExpanded = new Set(expandedExamples);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedExamples(newExpanded);
  };

  const resetExplorer = () => {
    setCurrentLayer(0);
    setExpandedExamples(new Set());
    setCompleted(false);
  };

  if (completed) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-4xl mx-auto bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl border border-emerald-200 p-8 shadow-sm"
      >
        <div className="text-center">
          <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-emerald-500" />
          </div>
          <h3 className="text-2xl font-bold text-charcoal mb-2">Concept Mastered!</h3>
          <p className="text-grey mb-6">You've explored all layers of {data.concept}</p>
          
          <div className="bg-white rounded-xl p-6 mb-6 text-left">
            <h4 className="font-semibold text-charcoal mb-4 flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-emerald-500" />
              Key Takeaways
            </h4>
            <ul className="space-y-2">
              {data.keyTakeaways.map((takeaway, index) => (
                <motion.li
                  key={index}
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-2 text-charcoal"
                >
                  <span className="text-emerald-500 mt-1">•</span>
                  <span>{takeaway}</span>
                </motion.li>
              ))}
            </ul>
          </div>
          
          <Button
            onClick={resetExplorer}
            variant="outline"
            className="border-emerald-500 text-emerald-600 hover:bg-emerald-50"
          >
            Explore Again
          </Button>
        </div>
      </motion.div>
    );
  }

  const layer = data.layers[currentLayer];
  const IconComponent = getIconComponent(layer.icon);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-50 to-teal-50 p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-emerald-500 rounded-lg p-3">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-charcoal">Concept Explorer</h3>
              <p className="text-grey">{data.concept}</p>
            </div>
          </div>
          <div className="text-sm text-grey">
            Layer {currentLayer + 1} of {data.layers.length}
          </div>
        </div>
      </div>

      {/* Progress */}
      <div className="bg-gray-100 h-2">
        <div 
          className="bg-gradient-to-r from-emerald-500 to-teal-500 h-full transition-all duration-500"
          style={{ width: `${((currentLayer + 1) / data.layers.length) * 100}%` }}
        />
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Definition (shown only on first layer) */}
        {currentLayer === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 bg-blue-50 border border-blue-200 rounded-xl p-6"
          >
            <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              What is {data.concept}?
            </h4>
            <p className="text-blue-700 leading-relaxed">{data.definition}</p>
          </motion.div>
        )}

        {/* Current Layer */}
        <motion.div
          key={currentLayer}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          <div className={cn(
            "rounded-xl p-6 border-2",
            layer.color === 'blue' && "bg-blue-50 border-blue-200",
            layer.color === 'green' && "bg-green-50 border-green-200",
            layer.color === 'purple' && "bg-purple-50 border-purple-200",
            layer.color === 'amber' && "bg-amber-50 border-amber-200"
          )}>
            <div className="flex items-center gap-3 mb-4">
              <div className={cn(
                "rounded-lg p-2",
                layer.color === 'blue' && "bg-blue-500",
                layer.color === 'green' && "bg-green-500",
                layer.color === 'purple' && "bg-purple-500",
                layer.color === 'amber' && "bg-amber-500"
              )}>
                <IconComponent className="h-5 w-5 text-white" />
              </div>
              <h4 className={cn(
                "font-semibold text-lg",
                layer.color === 'blue' && "text-blue-800",
                layer.color === 'green' && "text-green-800",
                layer.color === 'purple' && "text-purple-800",
                layer.color === 'amber' && "text-amber-800"
              )}>
                {layer.title}
              </h4>
            </div>
            <p className={cn(
              "leading-relaxed mb-4",
              layer.color === 'blue' && "text-blue-700",
              layer.color === 'green' && "text-green-700",
              layer.color === 'purple' && "text-purple-700",
              layer.color === 'amber' && "text-amber-700"
            )}>
              {layer.description}
            </p>
          </div>

          {/* Examples */}
          <div className="space-y-3">
            <h5 className="font-semibold text-charcoal flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Examples & Applications
            </h5>
            {layer.examples.map((example, index) => (
              <div key={index} className="border border-gray-200 rounded-lg">
                <button
                  onClick={() => toggleExample(index)}
                  className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <span className="font-medium text-charcoal">Example {index + 1}</span>
                  {expandedExamples.has(index) ? (
                    <ChevronDown className="h-4 w-4 text-grey" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-grey" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedExamples.has(index) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="overflow-hidden"
                    >
                      <div className="p-4 pt-0 border-t border-gray-100">
                        <p className="text-grey leading-relaxed">{example}</p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>

          {/* Real World Application */}
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20 rounded-xl p-6">
            <h5 className="font-semibold text-primary mb-3 flex items-center gap-2">
              <Users className="h-5 w-5" />
              Real-World Application
            </h5>
            <p className="text-charcoal leading-relaxed">{layer.realWorldApplication}</p>
          </div>
        </motion.div>
      </div>

      {/* Navigation */}
      <div className="bg-gray-50 p-6 flex justify-between items-center">
        <div className="flex gap-2">
          {data.layers.map((_, index) => (
            <div
              key={index}
              className={cn(
                "w-3 h-3 rounded-full transition-colors",
                index <= currentLayer ? "bg-emerald-500" : "bg-gray-300"
              )}
            />
          ))}
        </div>

        <Button
          onClick={handleLayerComplete}
          className="bg-emerald-500 hover:bg-emerald-600 text-white"
        >
          {currentLayer === data.layers.length - 1 ? "Complete Exploration" : "Next Layer"}
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
